/* Import Google Fonts - Poppins */
@import url('https://fonts.googleapis.com/css2?family=Poppins:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap');

/* Import AOS CSS */
@import 'aos/dist/aos.css';

/* Import Animate.css */
@import 'animate.css';

@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom CSS Variables for Theme */
:root {
    --primary-navy: #1e293b;
    --primary-cyan: #06b6d4;
    --primary-lavender: #a855f7;
    --primary-slate: #64748b;
    --gradient-primary: linear-gradient(135deg, #1e293b 0%, #06b6d4 50%, #a855f7 100%);
    --gradient-secondary: linear-gradient(135deg, #0f172a 0%, #1e293b 100%);
    --shadow-glass: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
    --backdrop-blur: blur(4px);
}

/* Dark mode variables */
[data-theme="dark"] {
    --bg-primary: #0f172a;
    --bg-secondary: #1e293b;
    --text-primary: #f8fafc;
    --text-secondary: #cbd5e1;
}

/* Light mode variables */
[data-theme="light"] {
    --bg-primary: #ffffff;
    --bg-secondary: #f8fafc;
    --text-primary: #1e293b;
    --text-secondary: #64748b;
}

/* Global Styles */
* {
    font-family: 'Poppins', sans-serif;
}

body {
    background: var(--bg-primary);
    color: var(--text-primary);
    transition: background-color 0.3s ease, color 0.3s ease;
}

/* Glassmorphism Effect */
.glass {
    background: rgba(255, 255, 255, 0.1);
    -webkit-backdrop-filter: var(--backdrop-blur);
    backdrop-filter: var(--backdrop-blur);
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: var(--shadow-glass);
}

/* Neumorphism Effect */
.neomorphism {
    background: linear-gradient(145deg, #e2e8f0, #cbd5e1);
    box-shadow: 20px 20px 60px #a8b4c0, -20px -20px 60px #ffffff;
}

/* Custom Scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: var(--bg-secondary);
}

::-webkit-scrollbar-thumb {
    background: var(--primary-cyan);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--primary-lavender);
}

/* Smooth transitions for all elements */
* {
    transition: all 0.3s ease;
}

/* Custom gradient text */
.gradient-text {
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* Loading skeleton */
.skeleton {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: loading 1.5s infinite;
}

@keyframes loading {
    0% {
        background-position: 200% 0;
    }
    100% {
        background-position: -200% 0;
    }
}

/* Custom hover effects */
.hover-lift {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.hover-lift:hover {
    transform: translateY(-8px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

/* Parallax container */
.parallax {
    background-attachment: fixed;
    background-position: center;
    background-repeat: no-repeat;
    background-size: cover;
}

/* Image Effects */
.image-container {
    position: relative;
    overflow: hidden;
    border-radius: 1rem;
}

.image-container img {
    transition: transform 0.5s ease, filter 0.3s ease;
    filter: brightness(0.9) contrast(1.1) saturate(1.2);
}

.image-container:hover img {
    transform: scale(1.05);
    filter: brightness(1) contrast(1.2) saturate(1.3);
}

.image-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(
        135deg,
        rgba(6, 182, 212, 0.1) 0%,
        rgba(139, 92, 246, 0.1) 100%
    );
    opacity: 0;
    transition: opacity 0.3s ease;
}

.image-container:hover .image-overlay {
    opacity: 1;
}

/* Shimmer Loading Effect */
.shimmer {
    background: linear-gradient(
        90deg,
        rgba(255, 255, 255, 0.1) 25%,
        rgba(255, 255, 255, 0.3) 50%,
        rgba(255, 255, 255, 0.1) 75%
    );
    background-size: 200% 100%;
    animation: shimmer 2s infinite;
}

@keyframes shimmer {
    0% {
        background-position: -200% 0;
    }
    100% {
        background-position: 200% 0;
    }
}

/* Smooth Fade In */
.fade-in {
    opacity: 0;
    transform: translateY(20px);
    animation: fadeIn 0.6s ease forwards;
}

@keyframes fadeIn {
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Profile Photo Styles */
.profile-photo {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.profile-photo:hover {
    transform: scale(1.05);
    box-shadow: 0 10px 25px rgba(6, 182, 212, 0.3);
}

/* Navigation Enhancements */
.nav-link {
    position: relative;
    overflow: hidden;
}

.nav-link::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(
        90deg,
        transparent,
        rgba(255, 255, 255, 0.1),
        transparent
    );
    transition: left 0.5s ease;
}

.nav-link:hover::before {
    left: 100%;
}

/* Mobile Menu Animation */
.mobile-menu-item {
    transform: translateX(-10px);
    opacity: 0;
    animation: slideInLeft 0.4s ease forwards;
}

.mobile-menu-item:nth-child(1) { animation-delay: 0.1s; }
.mobile-menu-item:nth-child(2) { animation-delay: 0.15s; }
.mobile-menu-item:nth-child(3) { animation-delay: 0.2s; }
.mobile-menu-item:nth-child(4) { animation-delay: 0.25s; }
.mobile-menu-item:nth-child(5) { animation-delay: 0.3s; }

@keyframes slideInLeft {
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* Enhanced Mobile Menu Styles */
.mobile-nav-active {
    background: rgba(6, 182, 212, 0.1);
    border-left: 4px solid #06b6d4;
}

/* Mobile Menu Item Enhancements */
.mobile-menu-item {
    position: relative;
    overflow: hidden;
}

.mobile-menu-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    transition: left 0.5s ease;
    z-index: 1;
}

.mobile-menu-item:hover::before {
    left: 100%;
}

/* Icon Container Animations */
.mobile-menu-item .w-12 {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.mobile-menu-item:hover .w-12 {
    transform: scale(1.1) rotate(5deg);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
}

/* Text Animations */
.mobile-menu-item span {
    transition: all 0.3s ease;
    position: relative;
    z-index: 2;
}

/* Custom Select Dropdown */
select {
    background-image: none;
}

/* Force dropdown direction on desktop */
@media (min-width: 768px) {
    select {
        /* Force dropdown to open downward on desktop */
        position: relative;
    }

    select:focus {
        /* Ensure dropdown opens below on desktop */
        z-index: 10;
    }
}

/* Form Focus States */
.form-focus:focus {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(6, 182, 212, 0.15);
}

/* Smooth Scrolling */
html {
    scroll-behavior: smooth;
}

/* Enhanced Button Animations */
.btn-primary {
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
}

.btn-primary::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    transition: width 0.6s ease, height 0.6s ease;
}

.btn-primary:hover::before {
    width: 300px;
    height: 300px;
}

/* Glassmorphism Enhancement */
.glass-enhanced {
    background: rgba(255, 255, 255, 0.08);
    -webkit-backdrop-filter: blur(20px);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.15);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

/* Enhanced Form Inputs */
.form-input {
    background: linear-gradient(135deg, rgba(51, 65, 85, 0.9), rgba(71, 85, 105, 0.9));
    border: 1px solid rgba(6, 182, 212, 0.3);
    color: white;
    transition: all 0.3s ease;
    -webkit-backdrop-filter: blur(10px);
    backdrop-filter: blur(10px);
}

.form-input:focus {
    outline: none;
    border-color: rgba(6, 182, 212, 0.6);
    box-shadow: 0 0 0 2px rgba(6, 182, 212, 0.2);
    background: linear-gradient(135deg, rgba(51, 65, 85, 0.95), rgba(71, 85, 105, 0.95));
}

.form-input::placeholder {
    color: rgba(209, 213, 219, 0.7);
}

.form-select {
    background: linear-gradient(135deg, rgba(51, 65, 85, 0.9), rgba(71, 85, 105, 0.9));
    border: 1px solid rgba(139, 92, 246, 0.3);
    color: white;
    transition: all 0.3s ease;
    -webkit-backdrop-filter: blur(10px);
    backdrop-filter: blur(10px);
}

.form-select:focus {
    outline: none;
    border-color: rgba(139, 92, 246, 0.6);
    box-shadow: 0 0 0 2px rgba(139, 92, 246, 0.2);
}

.form-select option {
    background: rgba(51, 65, 85, 1);
    color: white;
}

.form-textarea {
    background: linear-gradient(135deg, rgba(51, 65, 85, 0.9), rgba(71, 85, 105, 0.9));
    border: 1px solid rgba(16, 185, 129, 0.3);
    color: white;
    transition: all 0.3s ease;
    -webkit-backdrop-filter: blur(10px);
    backdrop-filter: blur(10px);
}

.form-textarea:focus {
    outline: none;
    border-color: rgba(16, 185, 129, 0.6);
    box-shadow: 0 0 0 2px rgba(16, 185, 129, 0.2);
}
