/* Import Google Fonts - Poppins */
@import url('https://fonts.googleapis.com/css2?family=Poppins:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap');

/* Import AOS CSS */
@import 'aos/dist/aos.css';

/* Import Animate.css */
@import 'animate.css';

@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom CSS Variables for Theme */
:root {
    --primary-navy: #1e293b;
    --primary-cyan: #06b6d4;
    --primary-lavender: #a855f7;
    --primary-slate: #64748b;
    --gradient-primary: linear-gradient(135deg, #1e293b 0%, #06b6d4 50%, #a855f7 100%);
    --gradient-secondary: linear-gradient(135deg, #0f172a 0%, #1e293b 100%);
    --shadow-glass: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
    --backdrop-blur: blur(4px);
}

/* Dark mode variables */
[data-theme="dark"] {
    --bg-primary: #0f172a;
    --bg-secondary: #1e293b;
    --text-primary: #f8fafc;
    --text-secondary: #cbd5e1;
}

/* Light mode variables */
[data-theme="light"] {
    --bg-primary: #ffffff;
    --bg-secondary: #f8fafc;
    --text-primary: #1e293b;
    --text-secondary: #64748b;
}

/* Global Styles */
* {
    font-family: 'Poppins', sans-serif;
}

body {
    background: var(--bg-primary);
    color: var(--text-primary);
    transition: background-color 0.3s ease, color 0.3s ease;
}

/* Glassmorphism Effect */
.glass {
    background: rgba(255, 255, 255, 0.1);
    -webkit-backdrop-filter: var(--backdrop-blur);
    backdrop-filter: var(--backdrop-blur);
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: var(--shadow-glass);
}

/* Neumorphism Effect */
.neomorphism {
    background: linear-gradient(145deg, #e2e8f0, #cbd5e1);
    box-shadow: 20px 20px 60px #a8b4c0, -20px -20px 60px #ffffff;
}

/* Custom Scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: var(--bg-secondary);
}

::-webkit-scrollbar-thumb {
    background: var(--primary-cyan);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--primary-lavender);
}

/* Smooth transitions for all elements */
* {
    transition: all 0.3s ease;
}

/* Custom gradient text */
.gradient-text {
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* Loading skeleton */
.skeleton {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: loading 1.5s infinite;
}

@keyframes loading {
    0% {
        background-position: 200% 0;
    }
    100% {
        background-position: -200% 0;
    }
}

/* Custom hover effects */
.hover-lift {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.hover-lift:hover {
    transform: translateY(-8px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

/* Parallax container */
.parallax {
    background-attachment: fixed;
    background-position: center;
    background-repeat: no-repeat;
    background-size: cover;
}
