{"$schema": "https://json.schemastore.org/package.json", "private": true, "type": "module", "scripts": {"build": "vite build", "dev": "vite"}, "devDependencies": {"@tailwindcss/forms": "^0.5.2", "@tailwindcss/vite": "^4.0.0", "alpinejs": "^3.14.9", "autoprefixer": "^10.4.2", "axios": "^1.8.2", "concurrently": "^9.0.1", "laravel-vite-plugin": "^1.2.0", "postcss": "^8.4.31", "tailwindcss": "^3.1.0", "vite": "^6.2.4"}, "dependencies": {"@alpinejs/intersect": "^3.14.9", "@alpinejs/persist": "^3.14.9", "@fontsource/poppins": "^5.2.6", "animate.css": "^4.1.1", "aos": "^2.3.4", "chart.js": "^4.5.0", "gsap": "^3.13.0", "lucide-react": "^0.525.0"}}