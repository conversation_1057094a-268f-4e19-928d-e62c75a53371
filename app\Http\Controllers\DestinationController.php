<?php

namespace App\Http\Controllers;

use App\Models\Destination;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

class DestinationController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $query = auth()->user()->destinations();

        // Search functionality
        if ($request->filled('search')) {
            $query->search($request->search);
        }

        // Filter by season
        if ($request->filled('musim')) {
            $query->bySeason($request->musim);
        }

        // Filter by mood
        if ($request->filled('mood')) {
            $query->byMood($request->mood);
        }

        // Filter by status
        if ($request->filled('status')) {
            if ($request->status === 'visited') {
                $query->visited();
            } elseif ($request->status === 'dream') {
                $query->dream();
            }
        }

        // Sort by
        $sortBy = $request->get('sort', 'created_at');
        $sortOrder = $request->get('order', 'desc');
        $query->orderBy($sortBy, $sortOrder);

        $destinations = $query->paginate(12)->withQueryString();

        return view('destinations.index', compact('destinations'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return view('destinations.create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'nama_tempat' => 'required|string|max:255',
            'negara' => 'required|string|max:255',
            'musim' => 'required|in:spring,summer,autumn,winter',
            'mood' => 'required|in:happy,healing,romantic,adventure,peaceful,exciting,cultural,nature',
            'status' => 'boolean',
            'deskripsi' => 'required|string',
            'gambar' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
        ]);

        $validated['user_id'] = auth()->id();
        $validated['status'] = $request->has('status');

        // Handle image upload
        if ($request->hasFile('gambar')) {
            $image = $request->file('gambar');
            $filename = time() . '_' . Str::slug($validated['nama_tempat']) . '.' . $image->getClientOriginalExtension();
            $path = $image->storeAs('destinasi', $filename, 'public');
            $validated['gambar'] = $filename;
        }

        Destination::create($validated);

        return redirect()->route('destinations.index')
            ->with('success', 'Destinasi berhasil ditambahkan!');
    }

    /**
     * Display the specified resource.
     */
    public function show(Destination $destination)
    {
        // Ensure user can only view their own destinations
        if ($destination->user_id !== auth()->id()) {
            abort(403);
        }

        return view('destinations.show', compact('destination'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Destination $destination)
    {
        // Ensure user can only edit their own destinations
        if ($destination->user_id !== auth()->id()) {
            abort(403);
        }

        return view('destinations.edit', compact('destination'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Destination $destination)
    {
        // Ensure user can only update their own destinations
        if ($destination->user_id !== auth()->id()) {
            abort(403);
        }

        $validated = $request->validate([
            'nama_tempat' => 'required|string|max:255',
            'negara' => 'required|string|max:255',
            'musim' => 'required|in:spring,summer,autumn,winter',
            'mood' => 'required|in:happy,healing,romantic,adventure,peaceful,exciting,cultural,nature',
            'status' => 'boolean',
            'deskripsi' => 'required|string',
            'gambar' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
        ]);

        $validated['status'] = $request->has('status');

        // Handle image upload
        if ($request->hasFile('gambar')) {
            // Delete old image if exists
            if ($destination->gambar && Storage::disk('public')->exists('destinasi/' . $destination->gambar)) {
                Storage::disk('public')->delete('destinasi/' . $destination->gambar);
            }

            $image = $request->file('gambar');
            $filename = time() . '_' . Str::slug($validated['nama_tempat']) . '.' . $image->getClientOriginalExtension();
            $path = $image->storeAs('destinasi', $filename, 'public');
            $validated['gambar'] = $filename;
        }

        $destination->update($validated);

        return redirect()->route('destinations.index')
            ->with('success', 'Destinasi berhasil diperbarui!');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Destination $destination)
    {
        // Ensure user can only delete their own destinations
        if ($destination->user_id !== auth()->id()) {
            abort(403);
        }

        // Delete image if exists
        if ($destination->gambar && Storage::disk('public')->exists('destinasi/' . $destination->gambar)) {
            Storage::disk('public')->delete('destinasi/' . $destination->gambar);
        }

        $destination->delete();

        return redirect()->route('destinations.index')
            ->with('success', 'Destinasi berhasil dihapus!');
    }
}
