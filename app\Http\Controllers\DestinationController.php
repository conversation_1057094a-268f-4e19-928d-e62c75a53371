<?php

namespace App\Http\Controllers;

use App\Models\Destination;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

class DestinationController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $query = auth()->user()->destinations();

        // Search functionality
        if ($request->filled('search')) {
            $query->search($request->search);
        }

        // Filter by season
        if ($request->filled('musim')) {
            $query->bySeason($request->musim);
        }

        // Filter by mood
        if ($request->filled('mood')) {
            $query->byMood($request->mood);
        }

        // Filter by status
        if ($request->filled('status')) {
            if ($request->status === 'visited') {
                $query->visited();
            } elseif ($request->status === 'dream') {
                $query->dream();
            }
        }

        // Sort by
        $sortBy = $request->get('sort', 'created_at');
        $sortOrder = $request->get('order', 'desc');
        $query->orderBy($sortBy, $sortOrder);

        $destinations = $query->paginate(12)->withQueryString();

        return view('destinations.index', compact('destinations'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return view('destinations.create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        // Increase limits for this request
        ini_set('memory_limit', '1024M');
        ini_set('max_execution_time', '600');

        // Enhanced POST size check
        if ($this->isPostTooLarge($request)) {
            return $this->handlePostTooLarge($request);
        }

        try {
            $validated = $request->validate([
                'nama_tempat' => 'required|string|max:255',
                'negara' => 'required|string|max:255',
                'musim' => 'required|in:spring,summer,autumn,winter',
                'mood' => 'required|in:happy,healing,romantic,adventure,peaceful,exciting,cultural,nature',
                'status' => 'boolean',
                'deskripsi' => 'required|string|max:1000',
                'gambar' => 'nullable|image|mimes:jpeg,jpg,png,gif,webp|max:10240', // 10MB
            ], [
                'nama_tempat.required' => 'Nama tempat harus diisi.',
                'negara.required' => 'Negara harus diisi.',
                'musim.required' => 'Musim terbaik harus dipilih.',
                'mood.required' => 'Mood perjalanan harus dipilih.',
                'deskripsi.required' => 'Deskripsi harus diisi.',
                'deskripsi.max' => 'Deskripsi tidak boleh lebih dari 1000 karakter.',
                'gambar.image' => 'File harus berupa gambar.',
                'gambar.mimes' => 'Format gambar harus JPEG, JPG, PNG, GIF, atau WEBP.',
                'gambar.max' => 'Ukuran gambar tidak boleh lebih dari 10MB.',
            ]);
        } catch (\Illuminate\Validation\ValidationException $e) {
            return back()->withErrors($e->errors())->withInput();
        }

        $validated['user_id'] = auth()->id();
        $validated['status'] = $request->has('status');

        // Handle image upload with proper error handling
        if ($request->hasFile('gambar')) {
            try {
                $image = $request->file('gambar');

                // Debug logging
                \Log::info('File upload attempt', [
                    'original_name' => $image->getClientOriginalName(),
                    'size' => $image->getSize(),
                    'mime_type' => $image->getMimeType(),
                    'is_valid' => $image->isValid()
                ]);

                // Additional file validation
                if (!$image->isValid()) {
                    \Log::error('Invalid file uploaded');
                    return back()->withErrors(['gambar' => 'File gambar tidak valid atau rusak.'])->withInput();
                }

                // Create unique filename
                $filename = time() . '_' . Str::slug($validated['nama_tempat']) . '.' . $image->getClientOriginalExtension();

                // Store file
                $path = $image->storeAs('destinasi', $filename, 'public');

                \Log::info('File storage result', [
                    'filename' => $filename,
                    'path' => $path,
                    'full_path' => storage_path('app/public/destinasi/' . $filename),
                    'exists' => file_exists(storage_path('app/public/destinasi/' . $filename))
                ]);

                if (!$path) {
                    \Log::error('Failed to store file');
                    return back()->withErrors(['gambar' => 'Gagal menyimpan gambar. Silakan coba lagi.'])->withInput();
                }

                $validated['gambar'] = $filename;

            } catch (\Exception $e) {
                \Log::error('File upload error: ' . $e->getMessage(), [
                    'trace' => $e->getTraceAsString()
                ]);
                return back()->withErrors(['gambar' => 'Terjadi kesalahan saat mengupload gambar. Silakan coba lagi.'])->withInput();
            }
        }

        try {
            Destination::create($validated);
        } catch (\Exception $e) {
            \Log::error('Database error: ' . $e->getMessage());
            return back()->withErrors(['error' => 'Terjadi kesalahan saat menyimpan data. Silakan coba lagi.'])->withInput();
        }

        return redirect()->route('destinations.index')
            ->with('success', 'Destinasi berhasil ditambahkan!');
    }

    /**
     * Display the specified resource.
     */
    public function show(Destination $destination)
    {
        // Ensure user can only view their own destinations
        if ($destination->user_id !== auth()->id()) {
            abort(403);
        }

        return view('destinations.show', compact('destination'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Destination $destination)
    {
        // Ensure user can only edit their own destinations
        if ($destination->user_id !== auth()->id()) {
            abort(403);
        }

        return view('destinations.edit', compact('destination'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Destination $destination)
    {
        // Ensure user can only update their own destinations
        if ($destination->user_id !== auth()->id()) {
            abort(403);
        }

        // Increase limits for this request
        ini_set('memory_limit', '1024M');
        ini_set('max_execution_time', '600');

        // Enhanced POST size check
        if ($this->isPostTooLarge($request)) {
            return $this->handlePostTooLarge($request);
        }

        try {
            $validated = $request->validate([
                'nama_tempat' => 'required|string|max:255',
                'negara' => 'required|string|max:255',
                'musim' => 'required|in:spring,summer,autumn,winter',
                'mood' => 'required|in:happy,healing,romantic,adventure,peaceful,exciting,cultural,nature',
                'status' => 'boolean',
                'deskripsi' => 'required|string|max:1000',
                'gambar' => 'nullable|image|mimes:jpeg,jpg,png,gif,webp|max:10240', // 10MB
            ], [
                'nama_tempat.required' => 'Nama tempat harus diisi.',
                'negara.required' => 'Negara harus diisi.',
                'musim.required' => 'Musim terbaik harus dipilih.',
                'mood.required' => 'Mood perjalanan harus dipilih.',
                'deskripsi.required' => 'Deskripsi harus diisi.',
                'deskripsi.max' => 'Deskripsi tidak boleh lebih dari 1000 karakter.',
                'gambar.image' => 'File harus berupa gambar.',
                'gambar.mimes' => 'Format gambar harus JPEG, JPG, PNG, GIF, atau WEBP.',
                'gambar.max' => 'Ukuran gambar tidak boleh lebih dari 10MB.',
            ]);
        } catch (\Illuminate\Validation\ValidationException $e) {
            return back()->withErrors($e->errors())->withInput();
        }

        $validated['status'] = $request->has('status');

        // Handle image upload with proper error handling
        if ($request->hasFile('gambar')) {
            try {
                $image = $request->file('gambar');

                // Additional file validation
                if (!$image->isValid()) {
                    return back()->withErrors(['gambar' => 'File gambar tidak valid atau rusak.'])->withInput();
                }

                // Delete old image if exists
                if ($destination->gambar && Storage::disk('public')->exists('destinasi/' . $destination->gambar)) {
                    Storage::disk('public')->delete('destinasi/' . $destination->gambar);
                }

                // Create unique filename
                $filename = time() . '_' . Str::slug($validated['nama_tempat']) . '.' . $image->getClientOriginalExtension();

                // Store file
                $path = $image->storeAs('destinasi', $filename, 'public');

                if (!$path) {
                    return back()->withErrors(['gambar' => 'Gagal menyimpan gambar. Silakan coba lagi.'])->withInput();
                }

                $validated['gambar'] = $filename;

            } catch (\Exception $e) {
                \Log::error('File upload error: ' . $e->getMessage());
                return back()->withErrors(['gambar' => 'Terjadi kesalahan saat mengupload gambar. Silakan coba lagi.'])->withInput();
            }
        }

        try {
            $destination->update($validated);
        } catch (\Exception $e) {
            \Log::error('Database error: ' . $e->getMessage());
            return back()->withErrors(['error' => 'Terjadi kesalahan saat memperbarui data. Silakan coba lagi.'])->withInput();
        }

        return redirect()->route('destinations.index')
            ->with('success', 'Destinasi berhasil diperbarui!');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Destination $destination)
    {
        // Ensure user can only delete their own destinations
        if ($destination->user_id !== auth()->id()) {
            abort(403);
        }

        // Delete image if exists
        if ($destination->gambar && Storage::disk('public')->exists('destinasi/' . $destination->gambar)) {
            Storage::disk('public')->delete('destinasi/' . $destination->gambar);
        }

        $destination->delete();

        return redirect()->route('destinations.index')
            ->with('success', 'Destinasi berhasil dihapus!');
    }

    /**
     * Check if POST data is too large
     */
    private function isPostTooLarge($request): bool
    {
        $contentLength = $request->server('CONTENT_LENGTH');
        $postMaxSize = $this->parseSize(ini_get('post_max_size'));

        return $contentLength && $contentLength > $postMaxSize;
    }

    /**
     * Handle POST too large error
     */
    private function handlePostTooLarge($request)
    {
        $contentLength = $request->server('CONTENT_LENGTH');
        $postMaxSize = ini_get('post_max_size');

        $actualSize = $this->formatBytes($contentLength);
        $maxSize = $this->formatBytes($this->parseSize($postMaxSize));

        return back()->withErrors([
            'gambar' => "Data yang dikirim terlalu besar ({$actualSize}). Maksimal yang diizinkan adalah {$maxSize}. Silakan kompres gambar atau pilih file yang lebih kecil."
        ])->withInput();
    }

    /**
     * Parse size string to bytes
     */
    private function parseSize(string $size): int
    {
        $size = trim($size);
        $last = strtolower($size[strlen($size) - 1]);
        $size = (int) $size;

        switch ($last) {
            case 'g':
                $size *= 1024;
            case 'm':
                $size *= 1024;
            case 'k':
                $size *= 1024;
        }

        return $size;
    }

    /**
     * Format bytes to human readable format
     */
    private function formatBytes($size, $precision = 2)
    {
        if ($size == 0) return '0 B';

        $base = log($size, 1024);
        $suffixes = ['B', 'KB', 'MB', 'GB', 'TB'];

        return round(pow(1024, $base - floor($base)), $precision) . ' ' . $suffixes[floor($base)];
    }
}
