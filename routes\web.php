<?php

use App\Http\Controllers\DestinationController;
use App\Http\Controllers\HomeController;
use App\Http\Controllers\ProfileController;
use Illuminate\Support\Facades\Route;

// Public routes
Route::get('/', [HomeController::class, 'index'])->name('home');
Route::get('/gallery', [HomeController::class, 'gallery'])->name('gallery');

// Help routes
Route::get('/help/guide', [HomeController::class, 'guide'])->name('help.guide');
Route::get('/help/faq', [HomeController::class, 'faq'])->name('help.faq');
Route::get('/help/contact', [HomeController::class, 'contact'])->name('help.contact');

// Protected routes
Route::middleware(['auth', 'verified'])->group(function () {
    Route::get('/dashboard', [HomeController::class, 'dashboard'])->name('dashboard');

    // Destination routes
    Route::resource('destinations', DestinationController::class);

    // Profile routes
    Route::get('/profile', [ProfileController::class, 'edit'])->name('profile.edit');
    Route::patch('/profile', [ProfileController::class, 'update'])->name('profile.update');
    Route::delete('/profile', [ProfileController::class, 'destroy'])->name('profile.destroy');
});

require __DIR__.'/auth.php';

// Chunked upload routes (fallback for large files)
Route::middleware(['auth'])->group(function () {
    Route::post('/api/upload-chunk', [DestinationController::class, 'uploadChunk'])->name('upload.chunk');
    Route::post('/api/finalize-upload', [DestinationController::class, 'finalizeUpload'])->name('upload.finalize');
    Route::post('/api/direct-upload', [DestinationController::class, 'directUpload'])->name('upload.direct');
});

// Image serving route (fallback for Windows symlink issues)
Route::get('/image/{filename}', function ($filename) {
    $path = storage_path('app/public/destinasi/' . $filename);

    if (!file_exists($path)) {
        abort(404);
    }

    $file = new \Symfony\Component\HttpFoundation\File\File($path);
    $mimeType = $file->getMimeType();

    return response()->file($path, [
        'Content-Type' => $mimeType,
        'Cache-Control' => 'public, max-age=31536000',
    ]);
})->name('image.serve');
