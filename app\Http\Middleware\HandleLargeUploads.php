<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class HandleLargeUploads
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Check for POST size limit before processing
        if ($this->isPostTooLarge($request)) {
            return $this->handlePostTooLarge($request);
        }

        // Increase time limits for large uploads
        if ($request->hasFile('gambar')) {
            set_time_limit(600); // 10 minutes
            ini_set('memory_limit', '1024M');
            ini_set('max_execution_time', '600');
        }

        return $next($request);
    }

    /**
     * Check if POST data is too large
     */
    private function isPostTooLarge(Request $request): bool
    {
        $contentLength = $request->server('CONTENT_LENGTH');
        $postMaxSize = $this->parseSize(ini_get('post_max_size'));

        return $contentLength && $contentLength > $postMaxSize;
    }

    /**
     * Handle POST too large error
     */
    private function handlePostTooLarge(Request $request)
    {
        $contentLength = $request->server('CONTENT_LENGTH');
        $postMaxSize = ini_get('post_max_size');

        $actualSize = $this->formatBytes($contentLength);
        $maxSize = $this->formatBytes($this->parseSize($postMaxSize));

        if ($request->expectsJson()) {
            return response()->json([
                'error' => 'Data yang dikirim terlalu besar',
                'message' => "Ukuran data: {$actualSize}, Maksimal: {$maxSize}",
                'actual_size' => $actualSize,
                'max_size' => $maxSize
            ], 413);
        }

        return back()->withErrors([
            'gambar' => "Data yang dikirim terlalu besar ({$actualSize}). Maksimal yang diizinkan adalah {$maxSize}. Silakan kompres gambar atau pilih file yang lebih kecil."
        ])->withInput();
    }

    /**
     * Parse size string to bytes
     */
    private function parseSize(string $size): int
    {
        $size = trim($size);
        $last = strtolower($size[strlen($size) - 1]);
        $size = (int) $size;

        switch ($last) {
            case 'g':
                $size *= 1024;
            case 'm':
                $size *= 1024;
            case 'k':
                $size *= 1024;
        }

        return $size;
    }

    /**
     * Format bytes to human readable format
     */
    private function formatBytes(int $size, int $precision = 2): string
    {
        if ($size == 0) return '0 B';

        $base = log($size, 1024);
        $suffixes = ['B', 'KB', 'MB', 'GB', 'TB'];

        return round(pow(1024, $base - floor($base)), $precision) . ' ' . $suffixes[floor($base)];
    }
}
