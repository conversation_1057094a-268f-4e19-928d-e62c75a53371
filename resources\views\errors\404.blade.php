<x-guest-layout>
    <div class="min-h-screen flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
        <div class="max-w-md w-full text-center">
            <!-- Error Icon -->
            <div class="flex justify-center mb-8">
                <div class="w-24 h-24 bg-gradient-to-r from-cyan-400 to-purple-500 rounded-3xl flex items-center justify-center">
                    <svg class="w-12 h-12 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.172 16.172a4 4 0 015.656 0M9 12h6m-6-4h6m2 5.291A7.962 7.962 0 0112 15c-2.34 0-4.467-.881-6.08-2.33M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9"></path>
                    </svg>
                </div>
            </div>

            <!-- Error Content -->
            <div class="glass rounded-2xl p-8">
                <h1 class="text-6xl font-bold gradient-text mb-4">404</h1>
                <h2 class="text-2xl font-bold text-white mb-4">Halaman Tidak Ditemukan</h2>
                <p class="text-gray-300 mb-8">
                    Maaf, halaman yang Anda cari tidak dapat ditemukan. Mungkin halaman telah dipindahkan atau URL yang Anda masukkan salah.
                </p>

                <!-- Action Buttons -->
                <div class="flex flex-col sm:flex-row gap-4 justify-center">
                    <a href="{{ route('home') }}" 
                       class="px-6 py-3 bg-gradient-to-r from-cyan-500 to-purple-500 rounded-xl text-white font-semibold hover:from-cyan-600 hover:to-purple-600 transform hover:scale-105 transition-all duration-300 shadow-lg">
                        Kembali ke Beranda
                    </a>
                    @auth
                        <a href="{{ route('dashboard') }}" 
                           class="px-6 py-3 glass rounded-xl text-white font-semibold hover:bg-white/20 transition-colors border border-white/30">
                            Dashboard
                        </a>
                    @else
                        <a href="{{ route('gallery') }}" 
                           class="px-6 py-3 glass rounded-xl text-white font-semibold hover:bg-white/20 transition-colors border border-white/30">
                            Galeri Destinasi
                        </a>
                    @endauth
                </div>
            </div>

            <!-- Additional Help -->
            <div class="mt-8 text-center">
                <p class="text-gray-400 text-sm mb-4">
                    Butuh bantuan? Hubungi kami atau kembali ke halaman utama.
                </p>
                <div class="flex justify-center space-x-6 text-gray-400">
                    <a href="{{ route('home') }}" class="hover:text-cyan-400 transition-colors">
                        <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M10.707 2.293a1 1 0 00-1.414 0l-7 7a1 1 0 001.414 1.414L4 10.414V17a1 1 0 001 1h2a1 1 0 001-1v-2a1 1 0 011-1h2a1 1 0 011 1v2a1 1 0 001 1h2a1 1 0 001-1v-6.586l.293.293a1 1 0 001.414-1.414l-7-7z"></path>
                        </svg>
                    </a>
                    <a href="{{ route('gallery') }}" class="hover:text-cyan-400 transition-colors">
                        <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" clip-rule="evenodd"></path>
                        </svg>
                    </a>
                    @auth
                        <a href="{{ route('destinations.index') }}" class="hover:text-cyan-400 transition-colors">
                            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z" clip-rule="evenodd"></path>
                            </svg>
                        </a>
                    @endauth
                </div>
            </div>
        </div>
    </div>
</x-guest-layout>
