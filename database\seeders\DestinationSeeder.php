<?php

namespace Database\Seeders;

use App\Models\Destination;
use App\Models\User;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class DestinationSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $users = User::all();

        $destinations = [
            [
                'nama_tempat' => 'Borobudur',
                'negara' => 'Indonesia',
                'musim' => 'summer',
                'mood' => 'cultural',
                'status' => true,
                'deskripsi' => 'Candi Buddha terbesar di dunia yang memukau dengan arsitektur dan sejarahnya yang luar biasa. Sunrise di Borobudur adalah pengalaman yang tak terlupakan.',
            ],
            [
                'nama_tempat' => 'Raja Ampat',
                'negara' => 'Indonesia',
                'musim' => 'spring',
                'mood' => 'nature',
                'status' => false,
                'deskripsi' => 'Kepulauan di Papua Barat yang dijuluki "The Crown Jewel of Marine Biodiversity". Rumah bagi 75% spesies karang dunia dan spot diving terbaik di planet ini dengan visibility hingga 30 meter.',
            ],
            [
                'nama_tempat' => 'Kyoto',
                'negara' => 'Jepang',
                'musim' => 'spring',
                'mood' => 'peaceful',
                'status' => false,
                'deskripsi' => 'Kota kuno Jepang dengan kuil-kuil indah dan taman bambu yang menenangkan. Sakura di musim semi menciptakan pemandangan yang magis.',
            ],
            [
                'nama_tempat' => 'Santorini',
                'negara' => 'Yunani',
                'musim' => 'summer',
                'mood' => 'romantic',
                'status' => false,
                'deskripsi' => 'Pulau vulkanik dengan bangunan putih dan sunset yang menakjubkan. Destinasi romantis yang sempurna untuk bulan madu.',
            ],
            [
                'nama_tempat' => 'Machu Picchu',
                'negara' => 'Peru',
                'musim' => 'autumn',
                'mood' => 'adventure',
                'status' => true,
                'deskripsi' => 'Kota kuno Inca yang tersembunyi di pegunungan Andes. Trekking menuju sini adalah petualangan yang menantang dan memuaskan.',
            ],
            [
                'nama_tempat' => 'Maldives',
                'negara' => 'Maladewa',
                'musim' => 'winter',
                'mood' => 'healing',
                'status' => false,
                'deskripsi' => 'Republik kepulauan dengan 1.192 atol karang di Samudra Hindia. Terkenal dengan water villa, laguna biru kristal, dan kehidupan laut yang eksotis. Surga tropis untuk honeymoon dan wellness retreat.',
            ],
            [
                'nama_tempat' => 'Northern Lights',
                'negara' => 'Islandia',
                'musim' => 'winter',
                'mood' => 'exciting',
                'status' => false,
                'deskripsi' => 'Aurora Borealis di Islandia, fenomena cahaya alami paling spektakuler di bumi. Terbaik dilihat September-Maret dengan aktivitas matahari tinggi. Kombinasi sempurna dengan landscape vulkanik dan geyser.',
            ],
            [
                'nama_tempat' => 'Bali',
                'negara' => 'Indonesia',
                'musim' => 'summer',
                'mood' => 'happy',
                'status' => true,
                'deskripsi' => 'Pulau dewata dengan pantai indah, budaya yang kaya, dan keramahan penduduk lokal. Destinasi favorit wisatawan dunia.',
            ],
            [
                'nama_tempat' => 'Swiss Alps',
                'negara' => 'Swiss',
                'musim' => 'winter',
                'mood' => 'adventure',
                'status' => false,
                'deskripsi' => 'Pegunungan Alpen Swiss dengan puncak Matterhorn (4.478m) dan Jungfraujoch "Top of Europe". Menawarkan ski world-class, hiking trails, dan kereta api scenic route paling indah di dunia.',
            ],
            [
                'nama_tempat' => 'Taj Mahal',
                'negara' => 'India',
                'musim' => 'autumn',
                'mood' => 'romantic',
                'status' => false,
                'deskripsi' => 'Monumen cinta yang megah dengan arsitektur Mughal yang memukau. Simbol cinta abadi yang menginspirasi jutaan orang.',
            ],
            [
                'nama_tempat' => 'Great Wall',
                'negara' => 'China',
                'musim' => 'spring',
                'mood' => 'cultural',
                'status' => true,
                'deskripsi' => 'Tembok raksasa yang membentang ribuan kilometer. Keajaiban dunia yang menunjukkan kehebatan peradaban manusia.',
            ],
            [
                'nama_tempat' => 'Amazon Rainforest',
                'negara' => 'Brazil',
                'musim' => 'summer',
                'mood' => 'nature',
                'status' => false,
                'deskripsi' => 'Hutan hujan Amazon seluas 5.5 juta km² yang membentang di 9 negara. Rumah bagi 10% spesies dunia, suku indigenous, dan sungai Amazon sepanjang 6.400 km. Ekspedisi eco-tourism yang mengubah hidup.',
            ],
            [
                'nama_tempat' => 'Eiffel Tower',
                'negara' => 'Prancis',
                'musim' => 'spring',
                'mood' => 'romantic',
                'status' => false,
                'deskripsi' => 'Menara ikonik Paris setinggi 330 meter yang dibangun tahun 1889. Symbol romantisme dan kemegahan arsitektur Prancis. Pemandangan kota Paris dari puncak menara sangat memukau, terutama saat sunset.',
            ],
            [
                'nama_tempat' => 'Petra',
                'negara' => 'Yordania',
                'musim' => 'autumn',
                'mood' => 'cultural',
                'status' => false,
                'deskripsi' => 'Kota kuno Nabatean yang dipahat dari batu merah muda. Situs UNESCO World Heritage yang dijuluki "Rose City". Treasury (Al-Khazneh) adalah facade paling terkenal dengan arsitektur Hellenistic yang menakjubkan.',
            ],
            [
                'nama_tempat' => 'Serengeti',
                'negara' => 'Tanzania',
                'musim' => 'summer',
                'mood' => 'adventure',
                'status' => false,
                'deskripsi' => 'Taman nasional dengan Great Migration - migrasi 2 juta wildebeest dan zebra. Rumah Big Five Africa dan pengalaman safari terbaik di dunia. Landscape savana yang tak berujung dengan sunset spektakuler.',
            ],
        ];

        foreach ($destinations as $index => $destinationData) {
            $user = $users->random();
            $destinationData['user_id'] = $user->id;

            Destination::create($destinationData);
        }
    }
}
