<?php

namespace Database\Seeders;

use App\Models\Destination;
use App\Models\User;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class DestinationSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $users = User::all();

        $destinations = [
            [
                'nama_tempat' => 'Borobudur',
                'negara' => 'Indonesia',
                'musim' => 'summer',
                'mood' => 'cultural',
                'status' => true,
                'deskripsi' => 'Candi Buddha terbesar di dunia yang memukau dengan arsitektur dan sejarahnya yang luar biasa. Sunrise di Borobudur adalah pengalaman yang tak terlupakan.',
            ],
            [
                'nama_tempat' => 'Raja Ampat',
                'negara' => 'Indonesia',
                'musim' => 'spring',
                'mood' => 'nature',
                'status' => false,
                'deskripsi' => 'Surga bawah laut dengan keanekaragaman hayati laut terkaya di dunia. Destinasi impian para penyelam dan pecinta alam.',
            ],
            [
                'nama_tempat' => 'Kyoto',
                'negara' => 'Jepang',
                'musim' => 'spring',
                'mood' => 'peaceful',
                'status' => false,
                'deskripsi' => 'Kota kuno Jepang dengan kuil-kuil indah dan taman bambu yang menenangkan. Sakura di musim semi menciptakan pemandangan yang magis.',
            ],
            [
                'nama_tempat' => 'Santorini',
                'negara' => 'Yunani',
                'musim' => 'summer',
                'mood' => 'romantic',
                'status' => false,
                'deskripsi' => 'Pulau vulkanik dengan bangunan putih dan sunset yang menakjubkan. Destinasi romantis yang sempurna untuk bulan madu.',
            ],
            [
                'nama_tempat' => 'Machu Picchu',
                'negara' => 'Peru',
                'musim' => 'autumn',
                'mood' => 'adventure',
                'status' => true,
                'deskripsi' => 'Kota kuno Inca yang tersembunyi di pegunungan Andes. Trekking menuju sini adalah petualangan yang menantang dan memuaskan.',
            ],
            [
                'nama_tempat' => 'Maldives',
                'negara' => 'Maladewa',
                'musim' => 'winter',
                'mood' => 'healing',
                'status' => false,
                'deskripsi' => 'Kepulauan tropis dengan air laut jernih dan resort mewah. Tempat yang sempurna untuk relaksasi dan healing dari rutinitas.',
            ],
            [
                'nama_tempat' => 'Northern Lights',
                'negara' => 'Islandia',
                'musim' => 'winter',
                'mood' => 'exciting',
                'status' => false,
                'deskripsi' => 'Fenomena alam aurora borealis yang memukau di langit malam. Pengalaman magis yang hanya bisa dinikmati di daerah kutub.',
            ],
            [
                'nama_tempat' => 'Bali',
                'negara' => 'Indonesia',
                'musim' => 'summer',
                'mood' => 'happy',
                'status' => true,
                'deskripsi' => 'Pulau dewata dengan pantai indah, budaya yang kaya, dan keramahan penduduk lokal. Destinasi favorit wisatawan dunia.',
            ],
            [
                'nama_tempat' => 'Swiss Alps',
                'negara' => 'Swiss',
                'musim' => 'winter',
                'mood' => 'adventure',
                'status' => false,
                'deskripsi' => 'Pegunungan Alpen dengan pemandangan salju yang spektakuler. Surga bagi pecinta ski dan pendakian gunung.',
            ],
            [
                'nama_tempat' => 'Taj Mahal',
                'negara' => 'India',
                'musim' => 'autumn',
                'mood' => 'romantic',
                'status' => false,
                'deskripsi' => 'Monumen cinta yang megah dengan arsitektur Mughal yang memukau. Simbol cinta abadi yang menginspirasi jutaan orang.',
            ],
            [
                'nama_tempat' => 'Great Wall',
                'negara' => 'China',
                'musim' => 'spring',
                'mood' => 'cultural',
                'status' => true,
                'deskripsi' => 'Tembok raksasa yang membentang ribuan kilometer. Keajaiban dunia yang menunjukkan kehebatan peradaban manusia.',
            ],
            [
                'nama_tempat' => 'Amazon Rainforest',
                'negara' => 'Brazil',
                'musim' => 'summer',
                'mood' => 'nature',
                'status' => false,
                'deskripsi' => 'Hutan hujan terbesar di dunia dengan keanekaragaman hayati yang luar biasa. Paru-paru dunia yang harus dilestarikan.',
            ],
        ];

        foreach ($destinations as $index => $destinationData) {
            $user = $users->random();
            $destinationData['user_id'] = $user->id;

            Destination::create($destinationData);
        }
    }
}
