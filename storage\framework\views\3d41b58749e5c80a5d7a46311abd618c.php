<?php if (isset($component)) { $__componentOriginal69dc84650370d1d4dc1b42d016d7226b = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal69dc84650370d1d4dc1b42d016d7226b = $attributes; } ?>
<?php $component = App\View\Components\GuestLayout::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('guest-layout'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\App\View\Components\GuestLayout::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
    <!-- Hero Section -->
    <section class="relative min-h-screen flex items-center justify-center overflow-hidden">
        <!-- Background Video/Image -->
        <div class="absolute inset-0 z-0">
            <div class="parallax bg-gradient-to-br from-slate-900 via-blue-900 to-purple-900 w-full h-full"></div>
            <div class="absolute inset-0 bg-black/30"></div>
        </div>

        <!-- Floating Elements -->
        <div class="absolute inset-0 overflow-hidden pointer-events-none">
            <div class="absolute top-20 left-10 w-20 h-20 bg-cyan-400/20 rounded-full blur-xl animate-pulse"></div>
            <div class="absolute top-40 right-20 w-32 h-32 bg-purple-500/20 rounded-full blur-2xl animate-pulse delay-1000"></div>
            <div class="absolute bottom-20 left-1/4 w-16 h-16 bg-pink-400/20 rounded-full blur-lg animate-pulse delay-500"></div>
        </div>

        <!-- Hero Content -->
        <div class="relative z-10 text-center px-4 sm:px-6 lg:px-8 max-w-6xl mx-auto">
            <h1 class="text-5xl md:text-7xl lg:text-8xl font-bold mb-8 animate__animated animate__fadeInUp" 
                data-aos="fade-up" data-aos-duration="1000">
                <span class="gradient-text">Dream</span>
                <span class="text-white">Destinations</span>
            </h1>
            
            <p class="text-xl md:text-2xl text-gray-300 mb-12 max-w-3xl mx-auto leading-relaxed animate__animated animate__fadeInUp animate__delay-1s" 
               data-aos="fade-up" data-aos-delay="200">
                Kelola dan wujudkan destinasi liburan impianmu. Catat, rencanakan, dan bagikan pengalaman perjalanan terbaikmu.
            </p>

            <div class="flex flex-col sm:flex-row gap-6 justify-center items-center animate__animated animate__fadeInUp animate__delay-2s" 
                 data-aos="fade-up" data-aos-delay="400">
                <?php if(auth()->guard()->check()): ?>
                    <a href="<?php echo e(route('dashboard')); ?>" 
                       class="px-8 py-4 bg-gradient-to-r from-cyan-500 to-purple-500 rounded-2xl text-white font-semibold text-lg hover:from-cyan-600 hover:to-purple-600 transform hover:scale-105 transition-all duration-300 shadow-2xl hover-lift">
                        Buka Dashboard
                    </a>
                    <a href="<?php echo e(route('destinations.create')); ?>" 
                       class="px-8 py-4 glass rounded-2xl text-white font-semibold text-lg hover:bg-white/20 transform hover:scale-105 transition-all duration-300 border border-white/30">
                        Tambah Destinasi
                    </a>
                <?php else: ?>
                    <a href="<?php echo e(route('register')); ?>" 
                       class="px-8 py-4 bg-gradient-to-r from-cyan-500 to-purple-500 rounded-2xl text-white font-semibold text-lg hover:from-cyan-600 hover:to-purple-600 transform hover:scale-105 transition-all duration-300 shadow-2xl hover-lift">
                        Mulai Sekarang
                    </a>
                    <a href="<?php echo e(route('login')); ?>" 
                       class="px-8 py-4 glass rounded-2xl text-white font-semibold text-lg hover:bg-white/20 transform hover:scale-105 transition-all duration-300 border border-white/30">
                        Masuk
                    </a>
                <?php endif; ?>
            </div>
        </div>

        <!-- Scroll Indicator -->
        <div class="absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce">
            <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 14l-7 7m0 0l-7-7m7 7V3"></path>
            </svg>
        </div>
    </section>

    <!-- Features Section -->
    <section class="py-20 bg-gradient-to-b from-slate-900 to-slate-800">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-16" data-aos="fade-up">
                <h2 class="text-4xl md:text-5xl font-bold text-white mb-6">
                    Fitur <span class="gradient-text">Unggulan</span>
                </h2>
                <p class="text-xl text-gray-300 max-w-3xl mx-auto">
                    Kelola destinasi impianmu dengan fitur-fitur canggih dan antarmuka yang intuitif
                </p>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                <!-- Feature 1 -->
                <div class="glass p-8 rounded-2xl hover-lift" data-aos="fade-up" data-aos-delay="100">
                    <div class="w-16 h-16 bg-gradient-to-r from-cyan-500 to-blue-500 rounded-2xl flex items-center justify-center mb-6">
                        <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                        </svg>
                    </div>
                    <h3 class="text-2xl font-bold text-white mb-4">Kelola Destinasi</h3>
                    <p class="text-gray-300">Catat dan atur destinasi impianmu dengan mudah. Tambahkan foto, deskripsi, dan detail perjalanan.</p>
                </div>

                <!-- Feature 2 -->
                <div class="glass p-8 rounded-2xl hover-lift" data-aos="fade-up" data-aos-delay="200">
                    <div class="w-16 h-16 bg-gradient-to-r from-purple-500 to-pink-500 rounded-2xl flex items-center justify-center mb-6">
                        <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                        </svg>
                    </div>
                    <h3 class="text-2xl font-bold text-white mb-4">Statistik Perjalanan</h3>
                    <p class="text-gray-300">Lihat statistik perjalananmu dengan grafik interaktif dan analisis mendalam.</p>
                </div>

                <!-- Feature 3 -->
                <div class="glass p-8 rounded-2xl hover-lift" data-aos="fade-up" data-aos-delay="300">
                    <div class="w-16 h-16 bg-gradient-to-r from-green-500 to-teal-500 rounded-2xl flex items-center justify-center mb-6">
                        <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                        </svg>
                    </div>
                    <h3 class="text-2xl font-bold text-white mb-4">Galeri Global</h3>
                    <p class="text-gray-300">Jelajahi destinasi dari pengguna lain dan dapatkan inspirasi untuk perjalanan berikutnya.</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Gallery Preview Section -->
    <?php if($featuredDestinations->count() > 0): ?>
    <section class="py-20 bg-slate-800">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-16" data-aos="fade-up">
                <h2 class="text-4xl md:text-5xl font-bold text-white mb-6">
                    Destinasi <span class="gradient-text">Inspiratif</span>
                </h2>
                <p class="text-xl text-gray-300 max-w-3xl mx-auto">
                    Jelajahi destinasi menakjubkan yang telah dibagikan oleh komunitas kami
                </p>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12">
                <?php $__currentLoopData = $featuredDestinations; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $destination): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <div class="glass rounded-2xl overflow-hidden hover-lift fade-in" data-aos="fade-up" data-aos-delay="<?php echo e($loop->index * 100); ?>">
                    <div class="aspect-video bg-gradient-to-br from-cyan-500 to-purple-500 relative image-container">
                        <img src="<?php echo e($destination->image_url); ?>"
                             alt="<?php echo e($destination->nama_tempat); ?>"
                             class="w-full h-full object-cover"
                             loading="lazy">
                        <div class="image-overlay"></div>
                        <div class="absolute inset-0 bg-black/20"></div>
                        <div class="absolute top-4 right-4">
                            <span class="px-3 py-1 bg-white/20 backdrop-blur-sm rounded-full text-white text-sm">
                                <?php echo e($destination->season_label); ?>

                            </span>
                        </div>
                    </div>
                    <div class="p-6">
                        <h3 class="text-xl font-bold text-white mb-2"><?php echo e($destination->nama_tempat); ?></h3>
                        <p class="text-gray-300 mb-4"><?php echo e($destination->negara); ?></p>
                        <div class="flex items-center justify-between">
                            <span class="px-3 py-1 bg-gradient-to-r from-cyan-500/20 to-purple-500/20 rounded-full text-cyan-300 text-sm">
                                <?php echo e($destination->mood_label); ?>

                            </span>
                            <span class="text-sm text-gray-400">
                                oleh <?php echo e($destination->user?->name ?? 'Unknown'); ?>

                            </span>
                        </div>
                    </div>
                </div>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </div>

            <div class="text-center" data-aos="fade-up">
                <a href="<?php echo e(route('gallery')); ?>"
                   class="px-8 py-4 bg-gradient-to-r from-cyan-500 to-purple-500 rounded-2xl text-white font-semibold text-lg hover:from-cyan-600 hover:to-purple-600 transform hover:scale-105 transition-all duration-300 shadow-2xl hover-lift">
                    Lihat Semua Destinasi
                </a>
            </div>
        </div>
    </section>
    <?php endif; ?>

    <!-- CTA Section -->
    <section class="py-20 bg-gradient-to-r from-cyan-600 to-purple-600">
        <div class="max-w-4xl mx-auto text-center px-4 sm:px-6 lg:px-8">
            <h2 class="text-4xl md:text-5xl font-bold text-white mb-6" data-aos="fade-up">
                Mulai Perjalanan Impianmu Hari Ini
            </h2>
            <p class="text-xl text-cyan-100 mb-8 max-w-2xl mx-auto" data-aos="fade-up" data-aos-delay="100">
                Bergabunglah dengan ribuan traveler yang telah mewujudkan destinasi impian mereka bersama Dream Destinations
            </p>
            <?php if(auth()->guard()->guest()): ?>
            <div class="flex flex-col sm:flex-row gap-4 justify-center" data-aos="fade-up" data-aos-delay="200">
                <a href="<?php echo e(route('register')); ?>"
                   class="px-8 py-4 bg-white text-purple-600 rounded-2xl font-semibold text-lg hover:bg-gray-100 transform hover:scale-105 transition-all duration-300 shadow-2xl">
                    Daftar Gratis
                </a>
                <a href="<?php echo e(route('login')); ?>"
                   class="px-8 py-4 border-2 border-white text-white rounded-2xl font-semibold text-lg hover:bg-white hover:text-purple-600 transform hover:scale-105 transition-all duration-300">
                    Masuk
                </a>
            </div>
            <?php endif; ?>
        </div>
    </section>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal69dc84650370d1d4dc1b42d016d7226b)): ?>
<?php $attributes = $__attributesOriginal69dc84650370d1d4dc1b42d016d7226b; ?>
<?php unset($__attributesOriginal69dc84650370d1d4dc1b42d016d7226b); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal69dc84650370d1d4dc1b42d016d7226b)): ?>
<?php $component = $__componentOriginal69dc84650370d1d4dc1b42d016d7226b; ?>
<?php unset($__componentOriginal69dc84650370d1d4dc1b42d016d7226b); ?>
<?php endif; ?>
<?php /**PATH C:\Users\<USER>\Herd\abdulsomadm\resources\views/welcome.blade.php ENDPATH**/ ?>