<IfModule mod_rewrite.c>
    <IfModule mod_negotiation.c>
        Options -MultiViews -Indexes
    </IfModule>

    RewriteEngine On

    # Handle Authorization Header
    RewriteCond %{HTTP:Authorization} .
    RewriteRule .* - [E=HTTP_AUTHORIZATION:%{HTTP:Authorization}]

    # Handle X-XSRF-Token Header
    RewriteCond %{HTTP:x-xsrf-token} .
    RewriteRule .* - [E=HTTP_X_XSRF_TOKEN:%{HTTP:X-XSRF-Token}]

    # Redirect Trailing Slashes If Not A Folder...
    RewriteCond %{REQUEST_FILENAME} !-d
    RewriteCond %{REQUEST_URI} (.+)/$
    RewriteRule ^ %1 [L,R=301]

    # Send Requests To Front Controller...
    RewriteCond %{REQUEST_FILENAME} !-d
    RewriteCond %{REQUEST_FILENAME} !-f
    RewriteRule ^ index.php [L]
</IfModule>

# Aggressive upload limits for file uploads
php_value upload_max_filesize 50M
php_value post_max_size 60M
php_value max_execution_time 600
php_value max_input_time 600
php_value memory_limit 1024M
php_value max_input_vars 10000
php_value max_file_uploads 50
php_admin_value file_uploads On

# Buffer settings
php_value output_buffering Off
php_value implicit_flush On

# Additional PHP settings
php_value auto_detect_line_endings On
php_value session.upload_progress.enabled On
php_value session.upload_progress.cleanup On

# Security headers for file uploads
<FilesMatch "\.(jpg|jpeg|png|gif|webp)$">
    Header set Cache-Control "public, max-age=31536000"
</FilesMatch>

# Prevent access to sensitive files
<FilesMatch "\.(env|ini|log)$">
    Order allow,deny
    Deny from all
</FilesMatch>
