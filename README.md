# 🌍 Dream Destinations

**Dream Destinations** adalah aplikasi web modern untuk mengelola dan merencanakan destinasi impian And<PERSON>. Dibangun dengan Laravel 11, <PERSON><PERSON>windCSS, dan teknologi web terkini.

## ✨ Fitur Utama

- 🏠 **Dashboard Interaktif** - Overview destinasi dengan statistik lengkap
- 📍 **Manajemen Destinasi** - CRUD lengkap untuk destinasi wisata
- 🖼️ **Upload Gambar** - Support upload gambar hingga 10MB dengan optimasi otomatis
- 🔍 **Pencarian & Filter** - Cari berdasarkan nama, negara, musim, dan mood
- 🌅 **Integrasi Unsplash** - Gambar otomatis dari Unsplash jika tidak upload
- 📱 **Responsive Design** - Optimal di desktop, tablet, dan mobile
- 🔐 **Autentikasi Aman** - Laravel Breeze dengan keamanan tinggi
- 🎨 **UI Modern** - Glassmorphism design dengan animasi smooth
- 🌐 **Multi-bahasa** - Interface dalam Bahasa Indonesia

## 🛠️ Teknologi

- **Backend**: <PERSON><PERSON> 11
- **Frontend**: TailwindCSS, Alpine.js
- **Database**: SQLite (development), MySQL (production)
- **Authentication**: Laravel Breeze
- **Build Tool**: Vite
- **Icons**: Heroicons
- **Animations**: AOS (Animate On Scroll)

## 📋 Persyaratan Sistem

- PHP >= 8.2
- Composer
- Node.js >= 18
- NPM atau Yarn
- MySQL (untuk production)

## 🚀 Instalasi

### 1. Clone Repository
```bash
git clone https://github.com/yourusername/dream-destinations.git
cd dream-destinations
```

### 2. Install Dependencies
```bash
# Install PHP dependencies
composer install

# Install Node.js dependencies
npm install
```

### 3. Konfigurasi Environment
```bash
# Copy environment file
cp .env.example .env

# Generate application key
php artisan key:generate
```

### 4. Konfigurasi Database
Edit file `.env` dan sesuaikan pengaturan database:
```env
DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=dream_destinations
DB_USERNAME=your_username
DB_PASSWORD=your_password
```

### 5. Migrasi Database
```bash
php artisan migrate
```

### 6. Create Storage Link
```bash
php artisan storage:link
```

### 7. Build Assets
```bash
npm run build
```

### 8. Jalankan Aplikasi
```bash
php artisan serve
```

Aplikasi akan tersedia di `http://localhost:8000`

## 🌐 Deployment ke Hostinger

### 1. Persiapan
```bash
# Jalankan script deployment
chmod +x deploy.sh
./deploy.sh
```

### 2. Upload ke Hosting
- Upload semua file ke directory hosting Anda
- Pastikan folder `public` adalah document root

### 3. Konfigurasi Production
Edit `.env` di server:
```env
APP_ENV=production
APP_DEBUG=false
APP_URL=https://yourdomain.com

# Database production
DB_CONNECTION=mysql
DB_HOST=localhost
DB_DATABASE=your_db_name
DB_USERNAME=your_db_user
DB_PASSWORD=your_db_password

# Mail configuration
MAIL_MAILER=smtp
MAIL_HOST=smtp.hostinger.com
MAIL_PORT=587
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your_email_password
```

### 4. Final Steps
```bash
# Generate new key
php artisan key:generate

# Run migrations
php artisan migrate --force

# Cache for production
php artisan config:cache
php artisan route:cache
php artisan view:cache
```

## 🔒 Keamanan

Aplikasi ini dilengkapi dengan fitur keamanan tingkat tinggi:

- ✅ **Security Headers** - XSS Protection, CSRF Protection, Content Security Policy
- ✅ **File Upload Security** - Validasi tipe file, ukuran, dan sanitasi
- ✅ **SQL Injection Protection** - Laravel Eloquent ORM
- ✅ **Authentication** - Laravel Breeze dengan bcrypt hashing
- ✅ **HTTPS Enforcement** - Strict Transport Security
- ✅ **Directory Protection** - .htaccess rules untuk melindungi file sensitif

## 📁 Struktur Project

```
dream-destinations/
├── app/
│   ├── Http/Controllers/     # Controllers
│   ├── Models/              # Eloquent Models
│   ├── Middleware/          # Custom Middleware
│   └── Services/            # Service Classes
├── resources/
│   ├── views/               # Blade Templates
│   ├── css/                 # Stylesheets
│   └── js/                  # JavaScript
├── public/
│   ├── build/               # Compiled Assets
│   └── storage/             # Public Storage Link
├── database/
│   ├── migrations/          # Database Migrations
│   └── seeders/             # Database Seeders
└── routes/
    └── web.php              # Web Routes
```

## 🎨 Fitur UI/UX

- **Glassmorphism Design** - Modern glass effect dengan backdrop blur
- **Smooth Animations** - Transisi halus dan hover effects
- **Responsive Layout** - Optimal di semua ukuran layar
- **Dark Theme** - Tema gelap yang elegan
- **Interactive Elements** - Hover states dan micro-interactions
- **Loading States** - Feedback visual untuk user actions

## 📸 Screenshot

[Tambahkan screenshot aplikasi di sini]

## 🤝 Kontribusi

1. Fork repository ini
2. Buat branch fitur (`git checkout -b feature/AmazingFeature`)
3. Commit perubahan (`git commit -m 'Add some AmazingFeature'`)
4. Push ke branch (`git push origin feature/AmazingFeature`)
5. Buat Pull Request

## 📄 Lisensi

Project ini menggunakan lisensi MIT. Lihat file `LICENSE` untuk detail.

## 👨‍💻 Author

**Abdul Somad**
- Email: <EMAIL>
- Phone: 081385191193

## 🙏 Acknowledgments

- Laravel Framework
- TailwindCSS
- Unsplash API
- Heroicons
- AOS Library

---

⭐ Jika project ini membantu Anda, jangan lupa berikan star di GitHub!
