# 🌐 Hostinger Deployment Guide

Panduan lengkap untuk deploy Dream Destinations ke Hostinger.

## 📋 Persiapan Sebelum Upload

### 1. Jalankan Deployment Script
```bash
chmod +x deploy.sh
./deploy.sh
```

### 2. Jalankan Security Check
```bash
php security-check.php
```

Pastikan security score minimal 90%.

## 🚀 Upload ke Hostinger

### 1. File Manager Upload
1. Login ke Hostinger Control Panel
2. Buka File Manager
3. Navigate ke `public_html` directory
4. Upload semua file project KECUALI:
   - `node_modules/`
   - `.git/`
   - `storage/logs/*.log`
   - File development (sudah di .gitignore)

### 2. Struktur Directory di Hostinger
```
public_html/
├── app/
├── bootstrap/
├── config/
├── database/
├── resources/
├── routes/
├── storage/
├── vendor/
├── public/
│   ├── index.php
│   ├── .htaccess
│   └── assets/
├── .env
├── .htaccess
├── artisan
├── composer.json
└── composer.lock
```

## ⚙️ Konfigurasi Environment

### 1. Edit .env File
```env
APP_NAME="Dream Destinations"
APP_ENV=production
APP_KEY=base64:YOUR_GENERATED_KEY_HERE
APP_DEBUG=false
APP_URL=https://yourdomain.com

# Database (Hostinger MySQL)
DB_CONNECTION=mysql
DB_HOST=localhost
DB_PORT=3306
DB_DATABASE=your_hostinger_db_name
DB_USERNAME=your_hostinger_db_user
DB_PASSWORD=your_hostinger_db_password

# Session
SESSION_DRIVER=database
SESSION_LIFETIME=120
SESSION_ENCRYPT=true

# Cache
CACHE_STORE=database

# Mail (Hostinger SMTP)
MAIL_MAILER=smtp
MAIL_HOST=smtp.hostinger.com
MAIL_PORT=587
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your_email_password
MAIL_ENCRYPTION=tls
MAIL_FROM_ADDRESS="<EMAIL>"
MAIL_FROM_NAME="${APP_NAME}"

# Unsplash API
UNSPLASH_ACCESS_KEY=your_unsplash_access_key
```

### 2. Generate Application Key
```bash
php artisan key:generate
```

## 🗄️ Database Setup

### 1. Create MySQL Database
1. Di Hostinger Control Panel, buka "MySQL Databases"
2. Create new database
3. Create database user
4. Assign user to database dengan ALL PRIVILEGES

### 2. Run Migrations
```bash
php artisan migrate --force
```

### 3. Create Storage Link
```bash
php artisan storage:link
```

## 🔧 Hostinger Specific Configuration

### 1. PHP Version
- Set PHP version ke 8.2 atau 8.3
- Enable required extensions:
  - mbstring
  - xml
  - ctype
  - json
  - bcmath
  - fileinfo
  - pdo_mysql
  - gd

### 2. File Permissions
```bash
chmod -R 755 storage
chmod -R 755 bootstrap/cache
chmod -R 775 storage/app/public
chmod -R 775 storage/framework
chmod -R 775 storage/logs
```

### 3. Cron Jobs (Optional)
Add cron job untuk Laravel scheduler:
```
* * * * * cd /home/<USER>/public_html && php artisan schedule:run >> /dev/null 2>&1
```

## 🛡️ Security Configuration

### 1. SSL Certificate
- Enable SSL di Hostinger Control Panel
- Force HTTPS redirect

### 2. Security Headers
Headers sudah dikonfigurasi di `.htaccess` dan middleware.

### 3. File Protection
Sensitive files sudah dilindungi oleh `.htaccess` rules.

## 🚀 Production Optimization

### 1. Cache Configuration
```bash
php artisan config:cache
php artisan route:cache
php artisan view:cache
```

### 2. Optimize Autoloader
```bash
composer install --optimize-autoloader --no-dev
```

### 3. Asset Optimization
Assets sudah di-build untuk production dengan `npm run build`.

## 🔍 Testing Deployment

### 1. Basic Functionality
- [ ] Homepage loads correctly
- [ ] User registration works
- [ ] User login works
- [ ] Dashboard accessible
- [ ] Create destination works
- [ ] Image upload works
- [ ] Edit destination works
- [ ] Delete destination works

### 2. Security Tests
- [ ] HTTPS redirect works
- [ ] Security headers present
- [ ] Sensitive files protected
- [ ] Error pages don't expose info
- [ ] File upload restrictions work

### 3. Performance Tests
- [ ] Page load times < 3 seconds
- [ ] Images load properly
- [ ] Mobile responsive
- [ ] CSS/JS assets load

## 🐛 Troubleshooting

### Common Issues

#### 1. 500 Internal Server Error
- Check file permissions
- Check .env configuration
- Check error logs in storage/logs/

#### 2. Database Connection Error
- Verify database credentials
- Check database server status
- Ensure database exists

#### 3. Storage Link Issues
- Run `php artisan storage:link`
- Check if public/storage symlink exists
- Verify storage permissions

#### 4. Asset Loading Issues
- Check if assets are built (`npm run build`)
- Verify public/.htaccess exists
- Check file permissions

#### 5. Email Not Working
- Verify SMTP credentials
- Check Hostinger email limits
- Test with simple mail

### Debug Commands
```bash
# Check application status
php artisan about

# Check configuration
php artisan config:show

# Check routes
php artisan route:list

# Check storage link
ls -la public/storage

# Check permissions
ls -la storage/
```

## 📞 Support

Jika mengalami masalah:
1. Check error logs di `storage/logs/laravel.log`
2. Verify semua konfigurasi sesuai guide
3. Contact Hostinger support untuk server issues
4. Check Laravel documentation untuk framework issues

## ✅ Post-Deployment Checklist

- [ ] SSL certificate active
- [ ] Domain pointing correctly
- [ ] Database connected
- [ ] Email sending works
- [ ] File uploads work
- [ ] All pages accessible
- [ ] Security headers active
- [ ] Performance optimized
- [ ] Backups configured
- [ ] Monitoring setup

🎉 **Selamat! Dream Destinations sudah live di Hostinger!**
