@echo off
REM Script to restart server and apply new PHP configurations

echo 🔄 Restarting server and applying new configurations...

echo 📋 Clearing Laravel caches...
php artisan config:clear
php artisan cache:clear
php artisan route:clear
php artisan view:clear

echo 🔧 Optimizing for production...
php artisan config:cache
php artisan route:cache
php artisan view:cache

echo 🏗️ Building assets...
npm run build

echo ✅ Server restart completed!
echo.
echo 📋 Next steps:
echo 1. Test file upload with small file first (< 1MB)
echo 2. Test with medium file (1-5MB)
echo 3. Test with large file (5-10MB)
echo 4. Check error logs if issues persist
echo.
echo 🌐 Open application: http://abdulsomadm.test/destinations/create
echo 🔍 Check config: http://abdulsomadm.test/check-config.php
echo.
pause
