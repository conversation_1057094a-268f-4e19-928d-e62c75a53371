; Local PHP Configuration Override
; This file provides local PHP configuration overrides

[PHP]
; File upload settings - Maximum possible
upload_max_filesize = 50M
post_max_size = 60M
max_file_uploads = 50

; Execution limits - Extended
max_execution_time = 600
max_input_time = 600
memory_limit = 1024M

; Input limits - Very high
max_input_vars = 10000
max_input_nesting_level = 128

; Buffer settings
output_buffering = Off
implicit_flush = On

; File handling
file_uploads = On
allow_url_fopen = On
auto_detect_line_endings = On

; Session upload progress
session.upload_progress.enabled = On
session.upload_progress.cleanup = On
session.upload_progress.prefix = "upload_progress_"
session.upload_progress.name = "PHP_SESSION_UPLOAD_PROGRESS"

; Error handling
log_errors = On
display_errors = Off
error_reporting = E_ALL & ~E_NOTICE & ~E_STRICT & ~E_DEPRECATED

; Security
expose_php = Off
allow_url_include = Off

; Performance
realpath_cache_size = 4096K
realpath_cache_ttl = 600

; Opcache settings (if available)
opcache.enable = 1
opcache.memory_consumption = 128
opcache.interned_strings_buffer = 8
opcache.max_accelerated_files = 4000
opcache.revalidate_freq = 2
opcache.fast_shutdown = 1
