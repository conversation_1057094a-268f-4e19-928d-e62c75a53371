<x-app-layout>
    <div class="py-8">
        <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
            <!-- Header -->
            <div class="glass rounded-2xl p-8 mb-8" data-aos="fade-up">
                <div class="flex items-center space-x-4">
                    <a href="{{ route('destinations.show', $destination) }}" 
                       class="p-2 glass rounded-lg hover:bg-white/20 transition-colors">
                        <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                        </svg>
                    </a>
                    <div>
                        <h1 class="text-3xl font-bold text-white mb-2">Edit Destinasi</h1>
                        <p class="text-gray-300">Perbarui informasi destinasi {{ $destination->nama_tempat }}</p>
                    </div>
                </div>
            </div>

            <!-- Current Image Preview -->
            <div class="glass rounded-2xl p-6 mb-8" data-aos="fade-up" data-aos-delay="50">
                <h3 class="text-lg font-semibold text-white mb-4">Gambar Saat Ini</h3>
                <div class="aspect-video max-w-md bg-gradient-to-br from-cyan-500 to-purple-500 rounded-lg overflow-hidden">
                    <img src="{{ $destination->image_url }}" 
                         alt="{{ $destination->nama_tempat }}" 
                         class="w-full h-full object-cover">
                </div>
            </div>

            <!-- Form -->
            <div class="glass rounded-2xl p-8" data-aos="fade-up" data-aos-delay="100">
                <form action="{{ route('destinations.update', $destination) }}" method="POST" enctype="multipart/form-data" class="space-y-6">
                    @csrf
                    @method('PUT')

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <!-- Nama Tempat -->
                        <div>
                            <label for="nama_tempat" class="block text-sm font-medium text-white mb-2">
                                Nama Tempat *
                            </label>
                            <input type="text" 
                                   id="nama_tempat" 
                                   name="nama_tempat" 
                                   value="{{ old('nama_tempat', $destination->nama_tempat) }}"
                                   placeholder="Contoh: Borobudur, Kyoto, Santorini"
                                   class="w-full px-4 py-3 bg-white/10 border border-white/20 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-cyan-500 @error('nama_tempat') border-red-500 @enderror"
                                   required>
                            @error('nama_tempat')
                                <p class="mt-1 text-sm text-red-400">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Negara -->
                        <div>
                            <label for="negara" class="block text-sm font-medium text-white mb-2">
                                Negara *
                            </label>
                            <input type="text" 
                                   id="negara" 
                                   name="negara" 
                                   value="{{ old('negara', $destination->negara) }}"
                                   placeholder="Contoh: Indonesia, Jepang, Yunani"
                                   class="w-full px-4 py-3 bg-white/10 border border-white/20 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-cyan-500 @error('negara') border-red-500 @enderror"
                                   required>
                            @error('negara')
                                <p class="mt-1 text-sm text-red-400">{{ $message }}</p>
                            @enderror
                        </div>
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <!-- Musim -->
                        <div>
                            <label for="musim" class="block text-sm font-medium text-white mb-2">
                                Musim Terbaik *
                            </label>
                            <select id="musim" 
                                    name="musim" 
                                    class="w-full px-4 py-3 bg-white/10 border border-white/20 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-cyan-500 @error('musim') border-red-500 @enderror"
                                    required>
                                <option value="">Pilih Musim</option>
                                @foreach(\App\Models\Destination::$seasons as $key => $label)
                                    <option value="{{ $key }}" {{ old('musim', $destination->musim) == $key ? 'selected' : '' }}>{{ $label }}</option>
                                @endforeach
                            </select>
                            @error('musim')
                                <p class="mt-1 text-sm text-red-400">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Mood -->
                        <div>
                            <label for="mood" class="block text-sm font-medium text-white mb-2">
                                Mood Perjalanan *
                            </label>
                            <select id="mood" 
                                    name="mood" 
                                    class="w-full px-4 py-3 bg-white/10 border border-white/20 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-cyan-500 @error('mood') border-red-500 @enderror"
                                    required>
                                <option value="">Pilih Mood</option>
                                @foreach(\App\Models\Destination::$moods as $key => $label)
                                    <option value="{{ $key }}" {{ old('mood', $destination->mood) == $key ? 'selected' : '' }}>{{ $label }}</option>
                                @endforeach
                            </select>
                            @error('mood')
                                <p class="mt-1 text-sm text-red-400">{{ $message }}</p>
                            @enderror
                        </div>
                    </div>

                    <!-- Status -->
                    <div>
                        <label class="flex items-center space-x-3">
                            <input type="checkbox" 
                                   name="status" 
                                   value="1"
                                   {{ old('status', $destination->status) ? 'checked' : '' }}
                                   class="w-5 h-5 text-cyan-500 bg-white/10 border-white/20 rounded focus:ring-cyan-500 focus:ring-2">
                            <span class="text-white">Sudah dikunjungi</span>
                        </label>
                        <p class="mt-1 text-sm text-gray-400">Centang jika kamu sudah pernah mengunjungi destinasi ini</p>
                    </div>

                    <!-- Deskripsi -->
                    <div>
                        <label for="deskripsi" class="block text-sm font-medium text-white mb-2">
                            Deskripsi *
                        </label>
                        <textarea id="deskripsi" 
                                  name="deskripsi" 
                                  rows="4"
                                  placeholder="Ceritakan tentang destinasi ini, mengapa kamu ingin mengunjunginya, atau pengalamanmu jika sudah pernah ke sana..."
                                  class="w-full px-4 py-3 bg-white/10 border border-white/20 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-cyan-500 @error('deskripsi') border-red-500 @enderror"
                                  required>{{ old('deskripsi', $destination->deskripsi) }}</textarea>
                        @error('deskripsi')
                            <p class="mt-1 text-sm text-red-400">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Upload Gambar -->
                    <div>
                        <label for="gambar" class="block text-sm font-medium text-white mb-2">
                            Ganti Gambar
                        </label>
                        <div class="relative">
                            <input type="file" 
                                   id="gambar" 
                                   name="gambar" 
                                   accept="image/*"
                                   class="w-full px-4 py-3 bg-white/10 border border-white/20 rounded-lg text-white file:mr-4 file:py-2 file:px-4 file:rounded-lg file:border-0 file:bg-gradient-to-r file:from-cyan-500 file:to-purple-500 file:text-white file:cursor-pointer hover:file:from-cyan-600 hover:file:to-purple-600 @error('gambar') border-red-500 @enderror">
                        </div>
                        <p class="mt-1 text-sm text-gray-400">
                            Opsional. Jika tidak diubah, gambar saat ini akan tetap digunakan.
                        </p>
                        @error('gambar')
                            <p class="mt-1 text-sm text-red-400">{{ $message }}</p>
                        @enderror
                        
                        <!-- Image Preview -->
                        <div id="imagePreview" class="mt-4 hidden">
                            <img id="previewImg" src="" alt="Preview" class="w-full max-w-md h-48 object-cover rounded-lg">
                        </div>
                    </div>

                    <!-- Submit Buttons -->
                    <div class="flex flex-col sm:flex-row gap-4 pt-6">
                        <button type="submit" 
                                class="flex-1 px-6 py-3 bg-gradient-to-r from-cyan-500 to-purple-500 rounded-xl text-white font-semibold hover:from-cyan-600 hover:to-purple-600 transform hover:scale-105 transition-all duration-300 shadow-lg">
                            Perbarui Destinasi
                        </button>
                        <a href="{{ route('destinations.show', $destination) }}" 
                           class="flex-1 px-6 py-3 bg-white/10 hover:bg-white/20 rounded-xl text-white font-semibold text-center transition-colors border border-white/20">
                            Batal
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Image Preview Script -->
    <script>
        document.getElementById('gambar').addEventListener('change', function(e) {
            const file = e.target.files[0];
            const preview = document.getElementById('imagePreview');
            const previewImg = document.getElementById('previewImg');
            
            if (file) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    previewImg.src = e.target.result;
                    preview.classList.remove('hidden');
                }
                reader.readAsDataURL(file);
            } else {
                preview.classList.add('hidden');
            }
        });
    </script>
</x-app-layout>
