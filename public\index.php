<?php

use Illuminate\Foundation\Application;
use Illuminate\Http\Request;

define('LARAVEL_START', microtime(true));

// AGGRESSIVE PHP LIMITS - Set as early as possible
ini_set('upload_max_filesize', '50M');
ini_set('post_max_size', '60M');
ini_set('memory_limit', '1024M');
ini_set('max_execution_time', '600');
ini_set('max_input_time', '600');
ini_set('max_input_vars', '10000');
ini_set('max_file_uploads', '50');
ini_set('file_uploads', '1');
ini_set('output_buffering', '0');
ini_set('implicit_flush', '1');

// Determine if the application is in maintenance mode...
if (file_exists($maintenance = __DIR__.'/../storage/framework/maintenance.php')) {
    require $maintenance;
}

// Register the Composer autoloader...
require __DIR__.'/../vendor/autoload.php';

// Bootstrap <PERSON>vel and handle the request...
/** @var Application $app */
$app = require_once __DIR__.'/../bootstrap/app.php';

$app->handleRequest(Request::capture());
