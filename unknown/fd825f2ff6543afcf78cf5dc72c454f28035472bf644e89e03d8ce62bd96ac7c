# Root .htaccess for PHP configuration
# This applies to the entire application

# Aggressive PHP settings for file uploads
php_value upload_max_filesize 50M
php_value post_max_size 60M
php_value max_execution_time 600
php_value max_input_time 600
php_value memory_limit 1024M
php_value max_input_vars 10000
php_value max_file_uploads 50
php_admin_value file_uploads On

# Buffer settings
php_value output_buffering Off
php_value implicit_flush On

# Session settings
php_value session.upload_progress.enabled On
php_value session.upload_progress.cleanup On

# Error handling
php_value log_errors On
php_value display_errors Off

# Security
php_value expose_php Off
php_value allow_url_include Off

# Performance
php_value realpath_cache_size 4096K
php_value realpath_cache_ttl 600

# Redirect to public directory
RewriteEngine On
RewriteCond %{REQUEST_URI} !^/public/
RewriteRule ^(.*)$ /public/$1 [L,QSA]
