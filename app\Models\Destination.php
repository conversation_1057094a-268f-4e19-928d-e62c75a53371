<?php

namespace App\Models;

use App\Services\UnsplashService;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Facades\Storage;

class Destination extends Model
{
    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'user_id',
        'nama_tempat',
        'negara',
        'musim',
        'mood',
        'status',
        'deskripsi',
        'gambar',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'status' => 'boolean',
    ];

    /**
     * Available seasons for destinations.
     *
     * @var array<string, string>
     */
    public static array $seasons = [
        'spring' => 'Musim Semi',
        'summer' => 'Musim Panas',
        'autumn' => 'Musim Gugur',
        'winter' => 'Musim Dingin',
    ];

    /**
     * Available moods for destinations.
     *
     * @var array<string, string>
     */
    public static array $moods = [
        'happy' => 'Bahagia',
        'healing' => '<PERSON>ye<PERSON>uhan',
        'romantic' => 'Romantis',
        'adventure' => 'Petualangan',
        'peaceful' => 'Damai',
        'exciting' => 'Menarik',
        'cultural' => 'Budaya',
        'nature' => 'Alam',
    ];

    /**
     * Get the user that owns the destination.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the full URL for the destination image.
     */
    public function getImageUrlAttribute(): string
    {
        if ($this->gambar && Storage::disk('public')->exists('destinasi/' . $this->gambar)) {
            return Storage::url('destinasi/' . $this->gambar);
        }

        // Fallback to Unsplash API
        return $this->getUnsplashImageUrl();
    }

    /**
     * Get Unsplash image URL based on country name.
     */
    public function getUnsplashImageUrl(): string
    {
        $unsplashService = app(UnsplashService::class);
        $query = $this->negara . ' ' . $this->nama_tempat;
        return $unsplashService->getImageUrl($query, 800, 600);
    }

    /**
     * Get the season label in Indonesian.
     */
    public function getSeasonLabelAttribute(): string
    {
        return self::$seasons[$this->musim] ?? $this->musim;
    }

    /**
     * Get the mood label in Indonesian.
     */
    public function getMoodLabelAttribute(): string
    {
        return self::$moods[$this->mood] ?? $this->mood;
    }

    /**
     * Get the status label in Indonesian.
     */
    public function getStatusLabelAttribute(): string
    {
        return $this->status ? 'Sudah Dikunjungi' : 'Impian';
    }

    /**
     * Scope a query to only include visited destinations.
     */
    public function scopeVisited($query)
    {
        return $query->where('status', true);
    }

    /**
     * Scope a query to only include dream destinations.
     */
    public function scopeDream($query)
    {
        return $query->where('status', false);
    }

    /**
     * Scope a query to filter by season.
     */
    public function scopeBySeason($query, $season)
    {
        return $query->where('musim', $season);
    }

    /**
     * Scope a query to filter by mood.
     */
    public function scopeByMood($query, $mood)
    {
        return $query->where('mood', $mood);
    }

    /**
     * Scope a query to search by name or country.
     */
    public function scopeSearch($query, $search)
    {
        return $query->where(function ($q) use ($search) {
            $q->where('nama_tempat', 'like', "%{$search}%")
              ->orWhere('negara', 'like', "%{$search}%")
              ->orWhere('deskripsi', 'like', "%{$search}%");
        });
    }
}
