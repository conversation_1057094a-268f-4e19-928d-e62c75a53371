<?php

namespace App\Models;

use App\Services\UnsplashService;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Facades\Storage;

class Destination extends Model
{
    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'user_id',
        'nama_tempat',
        'negara',
        'musim',
        'mood',
        'status',
        'deskripsi',
        'gambar',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'status' => 'boolean',
    ];

    /**
     * Available seasons for destinations.
     *
     * @var array<string, string>
     */
    public static array $seasons = [
        'spring' => 'Musim Semi',
        'summer' => 'Musim Panas',
        'autumn' => 'Musim Gugur',
        'winter' => 'Musim Dingin',
    ];

    /**
     * Available moods for destinations.
     *
     * @var array<string, string>
     */
    public static array $moods = [
        'happy' => 'Bahagia',
        'healing' => '<PERSON>ye<PERSON>uhan',
        'romantic' => 'Romantis',
        'adventure' => 'Petualangan',
        'peaceful' => 'Damai',
        'exciting' => 'Menarik',
        'cultural' => 'Budaya',
        'nature' => 'Alam',
    ];

    /**
     * Get the user that owns the destination.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the full URL for the destination image.
     */
    public function getImageUrlAttribute(): string
    {
        if ($this->gambar && Storage::disk('public')->exists('destinations/' . $this->gambar)) {
            return Storage::url('destinations/' . $this->gambar);
        }

        // Fallback to specific Unsplash images
        return $this->getUnsplashImageUrl();
    }

    /**
     * Get specific Unsplash image URL based on destination.
     */
    public function getUnsplashImageUrl(): string
    {
        $imageMap = [
            // Indonesia
            'Borobudur' => 'https://images.unsplash.com/photo-1596402184320-417e7178b2cd?w=800&h=600&fit=crop&auto=format&q=80',
            'Raja Ampat' => 'https://images.unsplash.com/photo-1583212292454-1fe6229603b7?w=800&h=600&fit=crop&auto=format&q=80',
            'Bali' => 'https://images.unsplash.com/photo-1537953773345-d172ccf13cf1?w=800&h=600&fit=crop&auto=format&q=80',

            // Japan
            'Kyoto' => 'https://images.unsplash.com/photo-1493976040374-85c8e12f0c0e?w=800&h=600&fit=crop&auto=format&q=80',

            // Greece
            'Santorini' => 'https://images.unsplash.com/photo-1570077188670-e3a8d69ac5ff?w=800&h=600&fit=crop&auto=format&q=80',

            // Peru
            'Machu Picchu' => 'https://images.unsplash.com/photo-1587595431973-160d0d94add1?w=800&h=600&fit=crop&auto=format&q=80',

            // Maldives
            'Maldives' => 'https://images.unsplash.com/photo-1506197603052-3cc9c3a201bd?w=800&h=600&fit=crop&auto=format&q=80',

            // Iceland
            'Northern Lights' => 'https://images.unsplash.com/photo-1531366936337-7c912a4589a7?w=800&h=600&fit=crop&auto=format&q=80',

            // Switzerland - Matterhorn
            'Swiss Alps' => 'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=800&h=600&fit=crop&auto=format&q=80',

            // India
            'Taj Mahal' => 'https://images.unsplash.com/photo-1564507592333-c60657eea523?w=800&h=600&fit=crop&auto=format&q=80',

            // China
            'Great Wall' => 'https://images.unsplash.com/photo-1508804185872-d7badad00f7d?w=800&h=600&fit=crop&auto=format&q=80',

            // Brazil
            'Amazon Rainforest' => 'https://images.unsplash.com/photo-1544735716-392fe2489ffa?w=800&h=600&fit=crop&auto=format&q=80',

            // France - Eiffel Tower (colorful sunset view)
            'Eiffel Tower' => 'https://images.unsplash.com/photo-1431274172761-fca41d930114?w=800&h=600&fit=crop&auto=format&q=80',

            // Jordan - Petra Treasury (Al-Khazneh facade)
            'Petra' => 'https://images.unsplash.com/photo-1539650116574-75c0c6d73f6e?w=800&h=600&fit=crop&auto=format&q=80',

            // Tanzania
            'Serengeti' => 'https://images.unsplash.com/photo-1516426122078-c23e76319801?w=800&h=600&fit=crop&auto=format&q=80',
        ];

        // Try to find specific image for this destination
        foreach ($imageMap as $place => $url) {
            if (stripos($this->nama_tempat, $place) !== false) {
                return $url;
            }
        }

        // Fallback to Unsplash API
        $unsplashService = app(UnsplashService::class);
        $query = $this->negara . ' ' . $this->nama_tempat;
        return $unsplashService->getImageUrl($query, 800, 600);
    }

    /**
     * Get the season label in Indonesian.
     */
    public function getSeasonLabelAttribute(): string
    {
        return self::$seasons[$this->musim] ?? $this->musim;
    }

    /**
     * Get the mood label in Indonesian.
     */
    public function getMoodLabelAttribute(): string
    {
        return self::$moods[$this->mood] ?? $this->mood;
    }

    /**
     * Get the status label in Indonesian.
     */
    public function getStatusLabelAttribute(): string
    {
        return $this->status ? 'Sudah Dikunjungi' : 'Impian';
    }

    /**
     * Scope a query to only include visited destinations.
     */
    public function scopeVisited($query)
    {
        return $query->where('status', true);
    }

    /**
     * Scope a query to only include dream destinations.
     */
    public function scopeDream($query)
    {
        return $query->where('status', false);
    }

    /**
     * Scope a query to filter by season.
     */
    public function scopeBySeason($query, $season)
    {
        return $query->where('musim', $season);
    }

    /**
     * Scope a query to filter by mood.
     */
    public function scopeByMood($query, $mood)
    {
        return $query->where('mood', $mood);
    }

    /**
     * Scope a query to search by name or country.
     */
    public function scopeSearch($query, $search)
    {
        return $query->where(function ($q) use ($search) {
            $q->where('nama_tempat', 'like', "%{$search}%")
              ->orWhere('negara', 'like', "%{$search}%")
              ->orWhere('deskripsi', 'like', "%{$search}%");
        });
    }
}
