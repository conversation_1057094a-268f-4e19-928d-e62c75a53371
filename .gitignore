*.log
.DS_Store
.env
.env.backup
.env.production
.phpactor.json
.phpunit.result.cache
/.fleet
/.idea
/.nova
/.phpunit.cache
/.vscode
/.zed
/auth.json
/node_modules
/public/build
/public/hot
/public/storage
/storage/*.key
/storage/pail
/vendor
Homestead.json
Homestead.yaml
Thumbs.db

# Additional files to ignore
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.npm
.yarn-integrity

# IDE files
*.swp
*.swo
*~

# OS generated files
.DS_Store?
ehthumbs.db
Icon?

# Laravel specific
/bootstrap/cache/*.php
/storage/framework/cache/*
/storage/framework/sessions/*
/storage/framework/views/*
/storage/logs/*

# Database
/database/database.sqlite
/database/database.sqlite-journal

# Security & Sensitive Files
*.pem
*.key
*.crt
*.p12
*.pfx
/config/secrets.php
/storage/app/private/*
/storage/app/public/uploads/*
!/storage/app/public/uploads/.gitkeep

# Backup files
*.bak
*.backup
*.old
*.orig

# Temporary files
*.tmp
*.temp
/temp/*
/tmp/*

# Debug files
/public/debug-*.php
/public/test-*.php

# Cache and compiled files
/bootstrap/cache/*.php
!/bootstrap/cache/.gitignore
/storage/framework/cache/data/*
!/storage/framework/cache/data/.gitignore

# Logs
/storage/logs/*.log
!/storage/logs/.gitignore

# Production specific
/public/mix-manifest.json
/public/js/app.js
/public/css/app.css
