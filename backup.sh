#!/bin/bash

# Dream Destinations Backup Script
# Creates comprehensive backup of application and database

echo "💾 Dream Destinations Backup Script"
echo "==================================="

# Configuration
BACKUP_DIR="backups"
DATE=$(date +%Y%m%d_%H%M%S)
APP_NAME="dream-destinations"
BACKUP_NAME="${APP_NAME}_${DATE}"

# Create backup directory
mkdir -p $BACKUP_DIR

echo "📦 Creating backup: $BACKUP_NAME"

# Create application backup (excluding unnecessary files)
echo "📁 Backing up application files..."
tar -czf "${BACKUP_DIR}/${BACKUP_NAME}_app.tar.gz" \
    --exclude='node_modules' \
    --exclude='.git' \
    --exclude='storage/logs/*.log' \
    --exclude='storage/framework/cache/*' \
    --exclude='storage/framework/sessions/*' \
    --exclude='storage/framework/views/*' \
    --exclude='vendor' \
    --exclude='public/build' \
    --exclude='backups' \
    .

# Backup database (if MySQL)
if [ -f ".env" ]; then
    DB_CONNECTION=$(grep "DB_CONNECTION=" .env | cut -d '=' -f2)
    DB_DATABASE=$(grep "DB_DATABASE=" .env | cut -d '=' -f2)
    DB_USERNAME=$(grep "DB_USERNAME=" .env | cut -d '=' -f2)
    DB_PASSWORD=$(grep "DB_PASSWORD=" .env | cut -d '=' -f2)
    DB_HOST=$(grep "DB_HOST=" .env | cut -d '=' -f2)

    if [ "$DB_CONNECTION" = "mysql" ]; then
        echo "🗄️ Backing up MySQL database..."
        mysqldump -h$DB_HOST -u$DB_USERNAME -p$DB_PASSWORD $DB_DATABASE > "${BACKUP_DIR}/${BACKUP_NAME}_database.sql"
    elif [ "$DB_CONNECTION" = "sqlite" ]; then
        echo "🗄️ Backing up SQLite database..."
        cp database/database.sqlite "${BACKUP_DIR}/${BACKUP_NAME}_database.sqlite"
    fi
fi

# Backup storage files
echo "📸 Backing up storage files..."
tar -czf "${BACKUP_DIR}/${BACKUP_NAME}_storage.tar.gz" storage/app/public/

# Create backup info file
echo "📋 Creating backup info..."
cat > "${BACKUP_DIR}/${BACKUP_NAME}_info.txt" << EOF
Dream Destinations Backup Information
=====================================

Backup Date: $(date)
Backup Name: $BACKUP_NAME
Application Version: $(git describe --tags --always 2>/dev/null || echo "Unknown")
Git Commit: $(git rev-parse HEAD 2>/dev/null || echo "Unknown")
Git Branch: $(git branch --show-current 2>/dev/null || echo "Unknown")

Files Included:
- Application files (${BACKUP_NAME}_app.tar.gz)
- Database dump (${BACKUP_NAME}_database.sql/sqlite)
- Storage files (${BACKUP_NAME}_storage.tar.gz)

Restore Instructions:
1. Extract application files to web directory
2. Import database from SQL dump
3. Extract storage files to storage/app/public/
4. Run: composer install --no-dev
5. Run: php artisan key:generate
6. Run: php artisan migrate
7. Run: php artisan storage:link
8. Set proper file permissions

Environment: $(grep "APP_ENV=" .env 2>/dev/null || echo "Unknown")
Debug Mode: $(grep "APP_DEBUG=" .env 2>/dev/null || echo "Unknown")
EOF

# Calculate backup sizes
APP_SIZE=$(du -h "${BACKUP_DIR}/${BACKUP_NAME}_app.tar.gz" | cut -f1)
STORAGE_SIZE=$(du -h "${BACKUP_DIR}/${BACKUP_NAME}_storage.tar.gz" | cut -f1)

if [ -f "${BACKUP_DIR}/${BACKUP_NAME}_database.sql" ]; then
    DB_SIZE=$(du -h "${BACKUP_DIR}/${BACKUP_NAME}_database.sql" | cut -f1)
elif [ -f "${BACKUP_DIR}/${BACKUP_NAME}_database.sqlite" ]; then
    DB_SIZE=$(du -h "${BACKUP_DIR}/${BACKUP_NAME}_database.sqlite" | cut -f1)
else
    DB_SIZE="N/A"
fi

echo ""
echo "✅ Backup completed successfully!"
echo "📊 Backup Summary:"
echo "   Application: $APP_SIZE"
echo "   Database: $DB_SIZE"
echo "   Storage: $STORAGE_SIZE"
echo "   Location: $BACKUP_DIR/"
echo ""
echo "📁 Backup files created:"
echo "   - ${BACKUP_NAME}_app.tar.gz"
echo "   - ${BACKUP_NAME}_database.sql/sqlite"
echo "   - ${BACKUP_NAME}_storage.tar.gz"
echo "   - ${BACKUP_NAME}_info.txt"
echo ""

# Clean old backups (keep last 5)
echo "🧹 Cleaning old backups (keeping last 5)..."
cd $BACKUP_DIR
ls -t ${APP_NAME}_*_app.tar.gz | tail -n +6 | xargs -r rm
ls -t ${APP_NAME}_*_database.* | tail -n +6 | xargs -r rm
ls -t ${APP_NAME}_*_storage.tar.gz | tail -n +6 | xargs -r rm
ls -t ${APP_NAME}_*_info.txt | tail -n +6 | xargs -r rm
cd ..

echo "💾 Backup process completed!"
echo "🔒 Store backups in a secure location"
echo "📅 Schedule regular backups for production"
