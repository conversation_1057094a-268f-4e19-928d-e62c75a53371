<?php if (isset($component)) { $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54 = $attributes; } ?>
<?php $component = App\View\Components\AppLayout::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('app-layout'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\App\View\Components\AppLayout::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
    <div class="py-8">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <!-- Header -->
            <div class="glass rounded-2xl p-8 mb-8" data-aos="fade-up">
                <div class="flex flex-col md:flex-row md:items-center md:justify-between">
                    <div class="mb-4 md:mb-0">
                        <h1 class="text-3xl font-bold text-white mb-2">Destinasi Saya</h1>
                        <p class="text-gray-300">Kelola dan atur destinasi liburan impianmu</p>
                    </div>
                    <a href="<?php echo e(route('destinations.create')); ?>" 
                       class="px-6 py-3 bg-gradient-to-r from-cyan-500 to-purple-500 rounded-xl text-white font-semibold hover:from-cyan-600 hover:to-purple-600 transform hover:scale-105 transition-all duration-300 shadow-lg">
                        + Tambah Destinasi
                    </a>
                </div>
            </div>

            <!-- Filters -->
            <div class="glass rounded-2xl p-6 mb-8" data-aos="fade-up" data-aos-delay="100">
                <form method="GET" action="<?php echo e(route('destinations.index')); ?>" class="grid grid-cols-1 md:grid-cols-5 gap-4">
                    <!-- Search -->
                    <div>
                        <input type="text" 
                               name="search" 
                               value="<?php echo e(request('search')); ?>"
                               placeholder="Cari destinasi..."
                               class="w-full px-4 py-2 bg-white/10 border border-white/20 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-cyan-500">
                    </div>

                    <!-- Season Filter -->
                    <div>
                        <select name="musim" class="w-full px-4 py-2 bg-white/10 border border-white/20 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-cyan-500">
                            <option value="">Semua Musim</option>
                            <?php $__currentLoopData = \App\Models\Destination::$seasons; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $label): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <option value="<?php echo e($key); ?>" <?php echo e(request('musim') == $key ? 'selected' : ''); ?>><?php echo e($label); ?></option>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </select>
                    </div>

                    <!-- Mood Filter -->
                    <div>
                        <select name="mood" class="w-full px-4 py-2 bg-white/10 border border-white/20 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-cyan-500">
                            <option value="">Semua Mood</option>
                            <?php $__currentLoopData = \App\Models\Destination::$moods; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $label): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <option value="<?php echo e($key); ?>" <?php echo e(request('mood') == $key ? 'selected' : ''); ?>><?php echo e($label); ?></option>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </select>
                    </div>

                    <!-- Status Filter -->
                    <div>
                        <select name="status" class="w-full px-4 py-2 bg-white/10 border border-white/20 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-cyan-500">
                            <option value="">Semua Status</option>
                            <option value="dream" <?php echo e(request('status') == 'dream' ? 'selected' : ''); ?>>Impian</option>
                            <option value="visited" <?php echo e(request('status') == 'visited' ? 'selected' : ''); ?>>Sudah Dikunjungi</option>
                        </select>
                    </div>

                    <!-- Submit Button -->
                    <div>
                        <button type="submit" 
                                class="w-full px-4 py-2 bg-gradient-to-r from-cyan-500 to-purple-500 rounded-lg text-white font-semibold hover:from-cyan-600 hover:to-purple-600 transition-all duration-300">
                            Filter
                        </button>
                    </div>
                </form>
            </div>

            <!-- Destinations Grid -->
            <?php if($destinations->count() > 0): ?>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-8">
                    <?php $__currentLoopData = $destinations; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $destination): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <div class="glass rounded-2xl overflow-hidden hover-lift fade-in" data-aos="fade-up" data-aos-delay="<?php echo e($loop->index * 100); ?>">
                        <div class="aspect-video bg-gradient-to-br from-cyan-500 to-purple-500 relative image-container">
                            <img src="<?php echo e($destination->image_url); ?>"
                                 alt="<?php echo e($destination->nama_tempat); ?>"
                                 class="w-full h-full object-cover"
                                 loading="lazy">
                            <div class="image-overlay"></div>
                            <div class="absolute inset-0 bg-black/20"></div>
                            <div class="absolute top-4 right-4">
                                <span class="px-3 py-1 bg-white/20 backdrop-blur-sm rounded-full text-white text-sm">
                                    <?php echo e($destination->season_label); ?>

                                </span>
                            </div>
                            <div class="absolute top-4 left-4">
                                <span class="px-3 py-1 <?php echo e($destination->status ? 'bg-green-500/80' : 'bg-yellow-500/80'); ?> backdrop-blur-sm rounded-full text-white text-sm">
                                    <?php echo e($destination->status_label); ?>

                                </span>
                            </div>
                        </div>
                        <div class="p-6">
                            <h3 class="text-xl font-bold text-white mb-2"><?php echo e($destination->nama_tempat); ?></h3>
                            <p class="text-gray-300 mb-4"><?php echo e($destination->negara); ?></p>
                            <p class="text-gray-400 text-sm mb-4 line-clamp-2"><?php echo e(Str::limit($destination->deskripsi, 100)); ?></p>
                            
                            <div class="flex items-center justify-between mb-4">
                                <span class="px-3 py-1 bg-gradient-to-r from-cyan-500/20 to-purple-500/20 rounded-full text-cyan-300 text-sm">
                                    <?php echo e($destination->mood_label); ?>

                                </span>
                                <span class="text-sm text-gray-400">
                                    <?php echo e($destination->created_at->diffForHumans()); ?>

                                </span>
                            </div>

                            <div class="flex space-x-2">
                                <a href="<?php echo e(route('destinations.show', $destination)); ?>" 
                                   class="flex-1 px-4 py-2 bg-white/10 hover:bg-white/20 rounded-lg text-white text-center transition-colors">
                                    Lihat
                                </a>
                                <a href="<?php echo e(route('destinations.edit', $destination)); ?>" 
                                   class="flex-1 px-4 py-2 bg-gradient-to-r from-cyan-500 to-purple-500 hover:from-cyan-600 hover:to-purple-600 rounded-lg text-white text-center transition-all">
                                    Edit
                                </a>
                                <form action="<?php echo e(route('destinations.destroy', $destination)); ?>" 
                                      method="POST" 
                                      class="flex-1"
                                      onsubmit="return confirm('Yakin ingin menghapus destinasi ini?')">
                                    <?php echo csrf_field(); ?>
                                    <?php echo method_field('DELETE'); ?>
                                    <button type="submit" 
                                            class="w-full px-4 py-2 bg-red-500 hover:bg-red-600 rounded-lg text-white transition-colors">
                                        Hapus
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </div>

                <!-- Pagination -->
                <div class="flex justify-center">
                    <?php echo e($destinations->links()); ?>

                </div>
            <?php else: ?>
                <div class="glass rounded-2xl p-12 text-center" data-aos="fade-up">
                    <svg class="w-20 h-20 text-gray-400 mx-auto mb-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                    </svg>
                    <h3 class="text-2xl font-bold text-white mb-4">Belum ada destinasi</h3>
                    <p class="text-gray-300 mb-8 max-w-md mx-auto">
                        Mulai perjalanan impianmu dengan menambahkan destinasi pertama. Catat tempat-tempat yang ingin kamu kunjungi!
                    </p>
                    <a href="<?php echo e(route('destinations.create')); ?>" 
                       class="px-8 py-4 bg-gradient-to-r from-cyan-500 to-purple-500 rounded-xl text-white font-semibold hover:from-cyan-600 hover:to-purple-600 transform hover:scale-105 transition-all duration-300 shadow-lg">
                        + Tambah Destinasi Pertama
                    </a>
                </div>
            <?php endif; ?>
        </div>
    </div>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $attributes = $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $component = $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php /**PATH C:\Users\<USER>\Herd\abdulsomadm\resources\views/destinations/index.blade.php ENDPATH**/ ?>