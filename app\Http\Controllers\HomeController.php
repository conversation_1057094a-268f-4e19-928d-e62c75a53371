<?php

namespace App\Http\Controllers;

use App\Models\Destination;
use Illuminate\Http\Request;

class HomeController extends Controller
{
    /**
     * Show the landing page.
     */
    public function index()
    {
        // Get random destinations for gallery preview
        $featuredDestinations = Destination::with('user')
            ->inRandomOrder()
            ->limit(6)
            ->get();

        return view('welcome', compact('featuredDestinations'));
    }

    /**
     * Show the dashboard.
     */
    public function dashboard()
    {
        $user = auth()->user();

        // Statistics
        $totalDestinations = $user->destinations()->count();
        $dreamDestinations = $user->dreamDestinations()->count();
        $visitedDestinations = $user->visitedDestinations()->count();

        // Recent destinations
        $recentDestinations = $user->destinations()
            ->latest()
            ->limit(5)
            ->get();

        // Statistics by mood
        $moodStats = $user->destinations()
            ->selectRaw('mood, COUNT(*) as count')
            ->groupBy('mood')
            ->pluck('count', 'mood')
            ->toArray();

        // Statistics by season
        $seasonStats = $user->destinations()
            ->selectRaw('musim, COUNT(*) as count')
            ->groupBy('musim')
            ->pluck('count', 'musim')
            ->toArray();

        return view('dashboard', compact(
            'totalDestinations',
            'dreamDestinations',
            'visitedDestinations',
            'recentDestinations',
            'moodStats',
            'seasonStats'
        ));
    }

    /**
     * Show global gallery of all destinations.
     */
    public function gallery(Request $request)
    {
        $query = Destination::with('user');

        // Search functionality
        if ($request->filled('search')) {
            $query->search($request->search);
        }

        // Filter by season
        if ($request->filled('musim')) {
            $query->bySeason($request->musim);
        }

        // Filter by mood
        if ($request->filled('mood')) {
            $query->byMood($request->mood);
        }

        $destinations = $query->latest()->paginate(15)->withQueryString();

        return view('gallery', compact('destinations'));
    }
}
