<?php if (isset($component)) { $__componentOriginal69dc84650370d1d4dc1b42d016d7226b = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal69dc84650370d1d4dc1b42d016d7226b = $attributes; } ?>
<?php $component = App\View\Components\GuestLayout::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('guest-layout'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\App\View\Components\GuestLayout::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
    <div class="min-h-screen py-12">
        <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
            <!-- Header -->
            <div class="text-center mb-12" data-aos="fade-up">
                <h1 class="text-4xl font-bold gradient-text mb-4">Frequently Asked Questions</h1>
                <p class="text-gray-300 text-lg">
                    Temukan jawaban untuk pertanyaan yang sering diajukan tentang Dream Destinations
                </p>
            </div>

            <!-- FAQ Content -->
            <div class="space-y-6" x-data="{ openFaq: null }">
                <!-- FAQ Item 1 -->
                <div class="glass rounded-2xl overflow-hidden" data-aos="fade-up" data-aos-delay="100">
                    <button @click="openFaq = openFaq === 1 ? null : 1" 
                            class="w-full px-8 py-6 text-left flex items-center justify-between hover:bg-white/5 transition-colors">
                        <h3 class="text-lg font-semibold text-white">Apa itu Dream Destinations?</h3>
                        <svg class="w-6 h-6 text-cyan-400 transform transition-transform" 
                             :class="{ 'rotate-180': openFaq === 1 }" 
                             fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                        </svg>
                    </button>
                    <div x-show="openFaq === 1" x-transition class="px-8 pb-6">
                        <p class="text-gray-300">
                            Dream Destinations adalah platform web untuk mengelola dan berbagi destinasi liburan impian Anda. 
                            Anda dapat menyimpan tempat-tempat yang ingin dikunjungi, melacak perjalanan yang sudah dilakukan, 
                            dan mendapatkan inspirasi dari pengguna lain.
                        </p>
                    </div>
                </div>

                <!-- FAQ Item 2 -->
                <div class="glass rounded-2xl overflow-hidden" data-aos="fade-up" data-aos-delay="200">
                    <button @click="openFaq = openFaq === 2 ? null : 2" 
                            class="w-full px-8 py-6 text-left flex items-center justify-between hover:bg-white/5 transition-colors">
                        <h3 class="text-lg font-semibold text-white">Apakah gratis untuk menggunakan platform ini?</h3>
                        <svg class="w-6 h-6 text-cyan-400 transform transition-transform" 
                             :class="{ 'rotate-180': openFaq === 2 }" 
                             fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                        </svg>
                    </button>
                    <div x-show="openFaq === 2" x-transition class="px-8 pb-6">
                        <p class="text-gray-300">
                            Ya, Dream Destinations sepenuhnya gratis untuk digunakan. Anda dapat mendaftar, menambahkan destinasi, 
                            dan menggunakan semua fitur tanpa biaya apapun.
                        </p>
                    </div>
                </div>

                <!-- FAQ Item 3 -->
                <div class="glass rounded-2xl overflow-hidden" data-aos="fade-up" data-aos-delay="300">
                    <button @click="openFaq = openFaq === 3 ? null : 3" 
                            class="w-full px-8 py-6 text-left flex items-center justify-between hover:bg-white/5 transition-colors">
                        <h3 class="text-lg font-semibold text-white">Bagaimana cara menambahkan destinasi baru?</h3>
                        <svg class="w-6 h-6 text-cyan-400 transform transition-transform" 
                             :class="{ 'rotate-180': openFaq === 3 }" 
                             fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                        </svg>
                    </button>
                    <div x-show="openFaq === 3" x-transition class="px-8 pb-6">
                        <p class="text-gray-300">
                            Setelah login, klik tombol "Tambah Destinasi" di dashboard atau halaman "Destinasi Saya". 
                            Isi form dengan informasi lengkap seperti nama tempat, negara, musim terbaik, mood perjalanan, 
                            dan deskripsi. Anda juga bisa mengupload gambar atau membiarkan sistem menggunakan gambar otomatis.
                        </p>
                    </div>
                </div>

                <!-- FAQ Item 4 -->
                <div class="glass rounded-2xl overflow-hidden" data-aos="fade-up" data-aos-delay="400">
                    <button @click="openFaq = openFaq === 4 ? null : 4" 
                            class="w-full px-8 py-6 text-left flex items-center justify-between hover:bg-white/5 transition-colors">
                        <h3 class="text-lg font-semibold text-white">Apa perbedaan antara status "Impian" dan "Sudah Dikunjungi"?</h3>
                        <svg class="w-6 h-6 text-cyan-400 transform transition-transform" 
                             :class="{ 'rotate-180': openFaq === 4 }" 
                             fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                        </svg>
                    </button>
                    <div x-show="openFaq === 4" x-transition class="px-8 pb-6">
                        <p class="text-gray-300">
                            Status "Impian" untuk tempat yang ingin Anda kunjungi di masa depan, sedangkan "Sudah Dikunjungi" 
                            untuk tempat yang sudah pernah Anda datangi. Ini membantu Anda melacak progress perjalanan dan 
                            merencanakan destinasi selanjutnya.
                        </p>
                    </div>
                </div>

                <!-- FAQ Item 5 -->
                <div class="glass rounded-2xl overflow-hidden" data-aos="fade-up" data-aos-delay="500">
                    <button @click="openFaq = openFaq === 5 ? null : 5" 
                            class="w-full px-8 py-6 text-left flex items-center justify-between hover:bg-white/5 transition-colors">
                        <h3 class="text-lg font-semibold text-white">Bagaimana cara mengupload foto profil?</h3>
                        <svg class="w-6 h-6 text-cyan-400 transform transition-transform" 
                             :class="{ 'rotate-180': openFaq === 5 }" 
                             fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                        </svg>
                    </button>
                    <div x-show="openFaq === 5" x-transition class="px-8 pb-6">
                        <p class="text-gray-300">
                            Klik nama Anda di navigation bar, pilih "Profil", lalu klik tombol "Choose File" di bagian foto profil. 
                            Pilih gambar dari komputer Anda (format: JPEG, PNG, JPG, GIF, maksimal 2MB), 
                            dan klik "Simpan Perubahan".
                        </p>
                    </div>
                </div>

                <!-- FAQ Item 6 -->
                <div class="glass rounded-2xl overflow-hidden" data-aos="fade-up" data-aos-delay="600">
                    <button @click="openFaq = openFaq === 6 ? null : 6" 
                            class="w-full px-8 py-6 text-left flex items-center justify-between hover:bg-white/5 transition-colors">
                        <h3 class="text-lg font-semibold text-white">Apa itu Galeri Global?</h3>
                        <svg class="w-6 h-6 text-cyan-400 transform transition-transform" 
                             :class="{ 'rotate-180': openFaq === 6 }" 
                             fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                        </svg>
                    </button>
                    <div x-show="openFaq === 6" x-transition class="px-8 pb-6">
                        <p class="text-gray-300">
                            Galeri Global adalah halaman yang menampilkan semua destinasi yang dibagikan oleh seluruh pengguna. 
                            Anda bisa menjelajahi destinasi dari pengguna lain untuk mendapatkan inspirasi perjalanan baru. 
                            Fitur ini dapat diakses tanpa perlu login.
                        </p>
                    </div>
                </div>

                <!-- FAQ Item 7 -->
                <div class="glass rounded-2xl overflow-hidden" data-aos="fade-up" data-aos-delay="700">
                    <button @click="openFaq = openFaq === 7 ? null : 7" 
                            class="w-full px-8 py-6 text-left flex items-center justify-between hover:bg-white/5 transition-colors">
                        <h3 class="text-lg font-semibold text-white">Bagaimana cara menghubungi support?</h3>
                        <svg class="w-6 h-6 text-cyan-400 transform transition-transform" 
                             :class="{ 'rotate-180': openFaq === 7 }" 
                             fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                        </svg>
                    </button>
                    <div x-show="openFaq === 7" x-transition class="px-8 pb-6">
                        <p class="text-gray-300">
                            Anda dapat menghubungi kami melalui halaman Kontak atau langsung via email di 
                            <a href="mailto:<EMAIL>" class="text-cyan-400 hover:text-cyan-300"><EMAIL></a> 
                            atau WhatsApp di <a href="tel:+6281385191193" class="text-cyan-400 hover:text-cyan-300">081385191193</a>.
                        </p>
                    </div>
                </div>
            </div>

            <!-- CTA -->
            <div class="text-center mt-12" data-aos="fade-up" data-aos-delay="800">
                <p class="text-gray-300 mb-6">Masih ada pertanyaan lain?</p>
                <a href="<?php echo e(route('help.contact')); ?>" class="inline-flex items-center px-8 py-4 bg-gradient-to-r from-cyan-500 to-purple-500 rounded-xl text-white font-semibold hover:from-cyan-600 hover:to-purple-600 transform hover:scale-105 transition-all duration-300 shadow-lg">
                    Hubungi Kami
                    <svg class="w-5 h-5 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
                    </svg>
                </a>
            </div>
        </div>
    </div>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal69dc84650370d1d4dc1b42d016d7226b)): ?>
<?php $attributes = $__attributesOriginal69dc84650370d1d4dc1b42d016d7226b; ?>
<?php unset($__attributesOriginal69dc84650370d1d4dc1b42d016d7226b); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal69dc84650370d1d4dc1b42d016d7226b)): ?>
<?php $component = $__componentOriginal69dc84650370d1d4dc1b42d016d7226b; ?>
<?php unset($__componentOriginal69dc84650370d1d4dc1b42d016d7226b); ?>
<?php endif; ?>
<?php /**PATH C:\Users\<USER>\Herd\abdulsomadm\resources\views/help/faq.blade.php ENDPATH**/ ?>