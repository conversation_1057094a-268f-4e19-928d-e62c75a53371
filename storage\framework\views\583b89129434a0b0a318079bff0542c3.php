<?php if (isset($component)) { $__componentOriginal69dc84650370d1d4dc1b42d016d7226b = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal69dc84650370d1d4dc1b42d016d7226b = $attributes; } ?>
<?php $component = App\View\Components\GuestLayout::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('guest-layout'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\App\View\Components\GuestLayout::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
    <div class="py-8">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <!-- Header -->
            <div class="text-center mb-12" data-aos="fade-up">
                <h1 class="text-4xl md:text-6xl font-bold text-white mb-6">
                    Galeri <span class="gradient-text">Destinasi</span>
                </h1>
                <p class="text-xl text-gray-300 max-w-3xl mx-auto">
                    Jelajahi destinasi menakjubkan yang telah dibagikan oleh komunitas Dream Destinations
                </p>
            </div>

            <!-- Filters -->
            <div class="glass rounded-2xl p-6 mb-8" data-aos="fade-up" data-aos-delay="100">
                <form method="GET" action="<?php echo e(route('gallery')); ?>" class="grid grid-cols-1 md:grid-cols-4 gap-4">
                    <!-- Search -->
                    <div class="relative">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <svg class="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                            </svg>
                        </div>
                        <input type="text"
                               name="search"
                               value="<?php echo e(request('search')); ?>"
                               placeholder="Cari destinasi..."
                               class="w-full pl-10 pr-4 py-3 bg-white/10 border border-white/20 rounded-xl text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-cyan-500 focus:border-cyan-500 transition-all duration-300 backdrop-blur-sm">
                    </div>

                    <!-- Season Filter -->
                    <div class="relative">
                        <select name="musim" class="w-full px-4 py-3 bg-white/10 border border-white/20 rounded-xl text-white focus:outline-none focus:ring-2 focus:ring-cyan-500 focus:border-cyan-500 transition-all duration-300 backdrop-blur-sm appearance-none cursor-pointer">
                            <option value="" class="bg-gray-800 text-white">🌍 Semua Musim</option>
                            <?php $__currentLoopData = \App\Models\Destination::$seasons; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $label): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <option value="<?php echo e($key); ?>" <?php echo e(request('musim') == $key ? 'selected' : ''); ?> class="bg-gray-800 text-white"><?php echo e($label); ?></option>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </select>
                        <div class="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                            <svg class="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                            </svg>
                        </div>
                    </div>

                    <!-- Mood Filter -->
                    <div class="relative">
                        <select name="mood" class="w-full px-4 py-3 bg-white/10 border border-white/20 rounded-xl text-white focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-purple-500 transition-all duration-300 backdrop-blur-sm appearance-none cursor-pointer">
                            <option value="" class="bg-gray-800 text-white">😊 Semua Mood</option>
                            <?php $__currentLoopData = \App\Models\Destination::$moods; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $label): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <option value="<?php echo e($key); ?>" <?php echo e(request('mood') == $key ? 'selected' : ''); ?> class="bg-gray-800 text-white"><?php echo e($label); ?></option>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </select>
                        <div class="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                            <svg class="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                            </svg>
                        </div>
                    </div>

                    <!-- Submit Button -->
                    <div>
                        <button type="submit" 
                                class="w-full px-4 py-2 bg-gradient-to-r from-cyan-500 to-purple-500 rounded-lg text-white font-semibold hover:from-cyan-600 hover:to-purple-600 transition-all duration-300">
                            Filter
                        </button>
                    </div>
                </form>
            </div>

            <!-- Gallery Grid -->
            <?php if($destinations->count() > 0): ?>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-8">
                    <?php $__currentLoopData = $destinations; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $destination): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <div class="glass rounded-2xl overflow-hidden hover-lift fade-in" data-aos="fade-up" data-aos-delay="<?php echo e($loop->index * 50); ?>">
                        <div class="aspect-video bg-gradient-to-br from-cyan-500 to-purple-500 relative image-container">
                            <img src="<?php echo e($destination->image_url); ?>"
                                 alt="<?php echo e($destination->nama_tempat); ?>"
                                 class="w-full h-full object-cover"
                                 loading="lazy">
                            <div class="image-overlay"></div>
                            <div class="absolute inset-0 bg-black/20"></div>
                            
                            <!-- Status Badge -->
                            <div class="absolute top-4 left-4">
                                <span class="px-3 py-1 <?php echo e($destination->status ? 'bg-green-500/80' : 'bg-yellow-500/80'); ?> backdrop-blur-sm rounded-full text-white text-sm font-semibold">
                                    <?php echo e($destination->status_label); ?>

                                </span>
                            </div>

                            <!-- Season Badge -->
                            <div class="absolute top-4 right-4">
                                <span class="px-3 py-1 bg-white/20 backdrop-blur-sm rounded-full text-white text-sm">
                                    <?php echo e($destination->season_label); ?>

                                </span>
                            </div>

                            <!-- Hover Overlay -->
                            <div class="absolute inset-0 bg-black/60 opacity-0 hover:opacity-100 transition-opacity duration-300 flex items-center justify-center">
                                <div class="text-center">
                                    <h3 class="text-xl font-bold text-white mb-2"><?php echo e($destination->nama_tempat); ?></h3>
                                    <p class="text-gray-200 mb-4"><?php echo e($destination->negara); ?></p>
                                    <span class="px-4 py-2 bg-gradient-to-r from-cyan-500 to-purple-500 rounded-full text-white text-sm font-semibold">
                                        <?php echo e($destination->mood_label); ?>

                                    </span>
                                </div>
                            </div>
                        </div>
                        
                        <div class="p-6">
                            <h3 class="text-xl font-bold text-white mb-2"><?php echo e($destination->nama_tempat); ?></h3>
                            <p class="text-gray-300 mb-3"><?php echo e($destination->negara); ?></p>
                            <p class="text-gray-400 text-sm mb-4 line-clamp-2"><?php echo e(Str::limit($destination->deskripsi, 80)); ?></p>
                            
                            <div class="flex items-center justify-between">
                                <div class="flex items-center space-x-2">
                                    <div class="w-8 h-8 bg-gradient-to-r from-cyan-400 to-purple-500 rounded-full flex items-center justify-center">
                                        <span class="text-white text-sm font-bold"><?php echo e(substr($destination->user?->name ?? 'U', 0, 1)); ?></span>
                                    </div>
                                    <span class="text-sm text-gray-400"><?php echo e($destination->user?->name ?? 'Unknown'); ?></span>
                                </div>
                                <span class="text-xs text-gray-500"><?php echo e($destination->created_at->diffForHumans()); ?></span>
                            </div>
                        </div>
                    </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </div>

                <!-- Pagination -->
                <div class="flex justify-center">
                    <?php echo e($destinations->links()); ?>

                </div>
            <?php else: ?>
                <div class="glass rounded-2xl p-12 text-center" data-aos="fade-up">
                    <svg class="w-20 h-20 text-gray-400 mx-auto mb-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                    </svg>
                    <h3 class="text-2xl font-bold text-white mb-4">Belum ada destinasi</h3>
                    <p class="text-gray-300 mb-8 max-w-md mx-auto">
                        Belum ada destinasi yang dibagikan. Jadilah yang pertama untuk berbagi destinasi impianmu!
                    </p>
                    <?php if(auth()->guard()->check()): ?>
                        <a href="<?php echo e(route('destinations.create')); ?>" 
                           class="px-8 py-4 bg-gradient-to-r from-cyan-500 to-purple-500 rounded-xl text-white font-semibold hover:from-cyan-600 hover:to-purple-600 transform hover:scale-105 transition-all duration-300 shadow-lg">
                            + Tambah Destinasi
                        </a>
                    <?php else: ?>
                        <a href="<?php echo e(route('register')); ?>" 
                           class="px-8 py-4 bg-gradient-to-r from-cyan-500 to-purple-500 rounded-xl text-white font-semibold hover:from-cyan-600 hover:to-purple-600 transform hover:scale-105 transition-all duration-300 shadow-lg">
                            Daftar Sekarang
                        </a>
                    <?php endif; ?>
                </div>
            <?php endif; ?>

            <!-- CTA Section -->
            <?php if(auth()->guard()->guest()): ?>
            <div class="mt-16 glass rounded-2xl p-8 text-center" data-aos="fade-up">
                <h3 class="text-2xl font-bold text-white mb-4">Ingin Berbagi Destinasi Impianmu?</h3>
                <p class="text-gray-300 mb-6 max-w-2xl mx-auto">
                    Bergabunglah dengan komunitas Dream Destinations dan bagikan destinasi impianmu kepada traveler lainnya.
                </p>
                <div class="flex flex-col sm:flex-row gap-4 justify-center">
                    <a href="<?php echo e(route('register')); ?>" 
                       class="px-6 py-3 bg-gradient-to-r from-cyan-500 to-purple-500 rounded-xl text-white font-semibold hover:from-cyan-600 hover:to-purple-600 transform hover:scale-105 transition-all duration-300 shadow-lg">
                        Daftar Gratis
                    </a>
                    <a href="<?php echo e(route('login')); ?>" 
                       class="px-6 py-3 glass rounded-xl text-white font-semibold hover:bg-white/20 transition-colors border border-white/30">
                        Masuk
                    </a>
                </div>
            </div>
            <?php endif; ?>
        </div>
    </div>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal69dc84650370d1d4dc1b42d016d7226b)): ?>
<?php $attributes = $__attributesOriginal69dc84650370d1d4dc1b42d016d7226b; ?>
<?php unset($__attributesOriginal69dc84650370d1d4dc1b42d016d7226b); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal69dc84650370d1d4dc1b42d016d7226b)): ?>
<?php $component = $__componentOriginal69dc84650370d1d4dc1b42d016d7226b; ?>
<?php unset($__componentOriginal69dc84650370d1d4dc1b42d016d7226b); ?>
<?php endif; ?>
<?php /**PATH C:\Users\<USER>\Herd\abdulsomadm\resources\views/gallery.blade.php ENDPATH**/ ?>