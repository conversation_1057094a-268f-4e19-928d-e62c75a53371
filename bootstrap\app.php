<?php

use Illuminate\Foundation\Application;
use Illuminate\Foundation\Configuration\Exceptions;
use Illuminate\Foundation\Configuration\Middleware;

return Application::configure(basePath: dirname(__DIR__))
    ->withRouting(
        web: __DIR__.'/../routes/web.php',
        commands: __DIR__.'/../routes/console.php',
        health: '/up',
    )
    ->withMiddleware(function (Middleware $middleware): void {
        // Add security headers middleware (highest priority)
        $middleware->web(prepend: [
            \App\Http\Middleware\SecurityHeaders::class,
            \App\Http\Middleware\HandleLargeUploads::class,
        ]);

        // Also add to global middleware stack
        $middleware->prepend([
            \App\Http\Middleware\SecurityHeaders::class,
            \App\Http\Middleware\HandleLargeUploads::class,
        ]);
    })
    ->withExceptions(function (Exceptions $exceptions): void {
        // Handle PostTooLargeException
        $exceptions->render(function (\Illuminate\Http\Exceptions\PostTooLargeException $e, $request) {
            $maxSize = ini_get('post_max_size');
            $contentLength = $request->server('CONTENT_LENGTH', 0);

            $actualSize = $contentLength ? round($contentLength / 1024 / 1024, 2) . 'MB' : 'Unknown';

            if ($request->expectsJson()) {
                return response()->json([
                    'error' => 'Data yang dikirim terlalu besar',
                    'message' => "Ukuran data: {$actualSize}, Maksimal: {$maxSize}",
                    'max_size' => $maxSize,
                    'actual_size' => $actualSize
                ], 413);
            }

            return back()->withErrors([
                'gambar' => "Data yang dikirim terlalu besar ({$actualSize}). Maksimal yang diizinkan adalah {$maxSize}. Silakan kompres gambar atau pilih file yang lebih kecil."
            ])->withInput();
        });
    })->create();
