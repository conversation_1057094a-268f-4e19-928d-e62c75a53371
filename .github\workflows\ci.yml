name: CI/CD Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

jobs:
  test:
    runs-on: ubuntu-latest
    
    services:
      sqlite:
        image: nouchka/sqlite3:latest
        
    steps:
    - uses: actions/checkout@v4
    
    - name: Setup PHP
      uses: shivammathur/setup-php@v2
      with:
        php-version: '8.2'
        extensions: mbstring, xml, ctype, iconv, intl, pdo_sqlite, dom, filter, gd, iconv, json, mbstring, pdo
        
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'
        cache: 'npm'
        
    - name: Copy .env
      run: php -r "file_exists('.env') || copy('.env.example', '.env');"
      
    - name: Install PHP Dependencies
      run: composer install -q --no-ansi --no-interaction --no-scripts --no-progress --prefer-dist
      
    - name: Install Node Dependencies
      run: npm ci
      
    - name: Generate key
      run: php artisan key:generate
      
    - name: Directory Permissions
      run: chmod -R 755 storage bootstrap/cache
      
    - name: Create Database
      run: |
        mkdir -p database
        touch database/database.sqlite
        
    - name: Execute tests (Unit and Feature tests) via PHPUnit
      env:
        DB_CONNECTION: sqlite
        DB_DATABASE: database/database.sqlite
      run: php artisan test
      
    - name: Build Assets
      run: npm run build
      
  deploy:
    needs: test
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Setup PHP
      uses: shivammathur/setup-php@v2
      with:
        php-version: '8.2'
        
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'
        cache: 'npm'
        
    - name: Install Dependencies
      run: |
        composer install --no-dev --optimize-autoloader
        npm ci
        
    - name: Build Assets
      run: npm run production
      
    - name: Deploy to Railway
      if: github.ref == 'refs/heads/main'
      run: |
        echo "🚀 Ready for deployment to Railway"
        echo "Configure Railway deployment in your repository settings"
