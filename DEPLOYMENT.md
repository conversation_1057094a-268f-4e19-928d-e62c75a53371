# 🚀 Deployment Guide - Dream Destinations

## ⚠️ Important Note

**Laravel adalah framework PHP yang membutuhkan server backend**, sehingga **tidak bisa di-deploy langsung ke GitHub Pages** yang hanya mendukung static files (HTML, CSS, JS).

## 🌐 Recommended Deployment Options

### 1. 🟢 Railway (Recommended)
**Free tier dengan database support**

```bash
# Install Railway CLI
npm install -g @railway/cli

# Login dan deploy
railway login
railway init
railway up
```

**Konfigurasi:**
- Environment: Production
- Database: PostgreSQL (auto-provisioned)
- Build Command: `composer install --no-dev && npm run build`
- Start Command: `php artisan serve --host=0.0.0.0 --port=$PORT`

### 2. 🟡 Vercel (Serverless)
**Dengan Vercel Functions untuk PHP**

```bash
# Install Vercel CLI
npm install -g vercel

# Deploy
vercel --prod
```

**File `vercel.json`:**
```json
{
  "functions": {
    "api/*.php": {
      "runtime": "vercel-php@0.6.0"
    }
  },
  "routes": [
    { "src": "/(.*)", "dest": "/api/index.php" }
  ]
}
```

### 3. 🟠 Netlify
**Dengan Netlify Functions**

```bash
# Install Netlify CLI
npm install -g netlify-cli

# Deploy
netlify deploy --prod
```

### 4. 🔵 Heroku
**Traditional hosting dengan database**

```bash
# Install Heroku CLI
# Create Procfile
echo "web: vendor/bin/heroku-php-apache2 public/" > Procfile

# Deploy
heroku create your-app-name
git push heroku main
```

## 📋 Pre-Deployment Checklist

### ✅ Environment Setup
- [ ] Copy `.env.example` to `.env`
- [ ] Set `APP_ENV=production`
- [ ] Set `APP_DEBUG=false`
- [ ] Generate `APP_KEY`
- [ ] Configure database connection
- [ ] Set proper `APP_URL`

### ✅ Database Setup
```bash
# Run migrations
php artisan migrate --force

# Seed database (optional)
php artisan db:seed --force
```

### ✅ Optimization
```bash
# Cache configuration
php artisan config:cache

# Cache routes
php artisan route:cache

# Cache views
php artisan view:cache

# Build assets
npm run build
```

### ✅ Security
- [ ] Remove `.env` from repository
- [ ] Set proper file permissions
- [ ] Configure HTTPS
- [ ] Set secure session settings

## 🔧 Production Environment Variables

```env
APP_NAME="Dream Destinations"
APP_ENV=production
APP_KEY=base64:your-generated-key
APP_DEBUG=false
APP_URL=https://your-domain.com

DB_CONNECTION=mysql
DB_HOST=your-db-host
DB_PORT=3306
DB_DATABASE=your-database
DB_USERNAME=your-username
DB_PASSWORD=your-password

# Cache & Session
CACHE_DRIVER=redis
SESSION_DRIVER=redis
QUEUE_CONNECTION=redis

# Mail (optional)
MAIL_MAILER=smtp
MAIL_HOST=your-smtp-host
MAIL_PORT=587
MAIL_USERNAME=your-email
MAIL_PASSWORD=your-password
```

## 🐳 Docker Deployment (Advanced)

**Dockerfile:**
```dockerfile
FROM php:8.2-fpm

# Install dependencies
RUN apt-get update && apt-get install -y \
    git \
    curl \
    libpng-dev \
    libonig-dev \
    libxml2-dev \
    zip \
    unzip

# Install PHP extensions
RUN docker-php-ext-install pdo_mysql mbstring exif pcntl bcmath gd

# Install Composer
COPY --from=composer:latest /usr/bin/composer /usr/bin/composer

# Set working directory
WORKDIR /var/www

# Copy application
COPY . .

# Install dependencies
RUN composer install --no-dev --optimize-autoloader

# Install Node.js and build assets
RUN curl -sL https://deb.nodesource.com/setup_18.x | bash -
RUN apt-get install -y nodejs
RUN npm install && npm run build

# Set permissions
RUN chown -R www-data:www-data /var/www
RUN chmod -R 755 /var/www/storage

EXPOSE 8000

CMD php artisan serve --host=0.0.0.0 --port=8000
```

## 🔍 Troubleshooting

### Common Issues:

1. **500 Internal Server Error**
   - Check file permissions
   - Verify `.env` configuration
   - Check error logs

2. **Database Connection Failed**
   - Verify database credentials
   - Check database server status
   - Ensure database exists

3. **Assets Not Loading**
   - Run `npm run build`
   - Check `APP_URL` setting
   - Verify asset paths

4. **Session Issues**
   - Clear cache: `php artisan cache:clear`
   - Check session driver configuration
   - Verify storage permissions

## 📞 Support

Jika mengalami masalah deployment:
1. Check Laravel logs: `storage/logs/laravel.log`
2. Check server error logs
3. Verify environment configuration
4. Test locally first

## 🎯 Quick Deploy Commands

```bash
# Prepare for deployment
composer install --no-dev --optimize-autoloader
npm run build
php artisan config:cache
php artisan route:cache
php artisan view:cache

# Deploy to production
git add .
git commit -m "Deploy to production"
git push origin main
```

---

**Note**: Pilih platform deployment yang sesuai dengan kebutuhan dan budget Anda. Railway dan Vercel menawarkan free tier yang cukup untuk project ini.
