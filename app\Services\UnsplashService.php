<?php

namespace App\Services;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class UnsplashService
{
    private string $accessKey;
    private string $baseUrl = 'https://api.unsplash.com';

    public function __construct()
    {
        $this->accessKey = config('services.unsplash.access_key', env('UNSPLASH_ACCESS_KEY'));
    }

    /**
     * Get image URL from Unsplash based on search query.
     */
    public function getImageUrl(string $query, int $width = 800, int $height = 600): string
    {
        try {
            if (empty($this->accessKey) || $this->accessKey === 'your_unsplash_access_key_here') {
                // Fallback to Unsplash Source API (no API key required)
                return $this->getSourceImageUrl($query, $width, $height);
            }

            $response = Http::withHeaders([
                'Authorization' => 'Client-ID ' . $this->accessKey,
            ])->get($this->baseUrl . '/search/photos', [
                'query' => $query,
                'per_page' => 1,
                'orientation' => 'landscape',
            ]);

            if ($response->successful()) {
                $data = $response->json();
                
                if (!empty($data['results'])) {
                    $photo = $data['results'][0];
                    return $photo['urls']['regular'] ?? $photo['urls']['small'];
                }
            }

            // Fallback to source API if search fails
            return $this->getSourceImageUrl($query, $width, $height);

        } catch (\Exception $e) {
            Log::error('Unsplash API error: ' . $e->getMessage());
            return $this->getSourceImageUrl($query, $width, $height);
        }
    }

    /**
     * Get image URL from Unsplash Source API (no API key required).
     */
    private function getSourceImageUrl(string $query, int $width = 800, int $height = 600): string
    {
        $encodedQuery = urlencode($query);
        return "https://source.unsplash.com/{$width}x{$height}/?{$encodedQuery}";
    }

    /**
     * Get multiple images for a query.
     */
    public function getImages(string $query, int $count = 10): array
    {
        try {
            if (empty($this->accessKey) || $this->accessKey === 'your_unsplash_access_key_here') {
                // Return multiple source URLs
                $images = [];
                for ($i = 0; $i < $count; $i++) {
                    $images[] = [
                        'url' => $this->getSourceImageUrl($query . ' ' . $i),
                        'alt' => $query,
                    ];
                }
                return $images;
            }

            $response = Http::withHeaders([
                'Authorization' => 'Client-ID ' . $this->accessKey,
            ])->get($this->baseUrl . '/search/photos', [
                'query' => $query,
                'per_page' => $count,
                'orientation' => 'landscape',
            ]);

            if ($response->successful()) {
                $data = $response->json();
                
                return collect($data['results'] ?? [])->map(function ($photo) {
                    return [
                        'url' => $photo['urls']['regular'] ?? $photo['urls']['small'],
                        'alt' => $photo['alt_description'] ?? $photo['description'] ?? '',
                        'photographer' => $photo['user']['name'] ?? '',
                        'photographer_url' => $photo['user']['links']['html'] ?? '',
                    ];
                })->toArray();
            }

            return [];

        } catch (\Exception $e) {
            Log::error('Unsplash API error: ' . $e->getMessage());
            return [];
        }
    }

    /**
     * Get random image URL.
     */
    public function getRandomImageUrl(string $query = '', int $width = 800, int $height = 600): string
    {
        if (empty($query)) {
            return "https://source.unsplash.com/{$width}x{$height}/";
        }

        return $this->getImageUrl($query, $width, $height);
    }
}
