<section class="space-y-6">
    <div class="p-4 bg-red-500/10 border border-red-500/20 rounded-lg">
        <p class="text-red-300 text-sm mb-4">
            Set<PERSON>h akun <PERSON>a di<PERSON>, semua sumber daya dan data akan dihapus secara permanen. Sebelum menghapus akun, silakan unduh data atau informasi yang ingin Anda simpan.
        </p>

        <button x-data=""
                x-on:click.prevent="$dispatch('open-modal', 'confirm-user-deletion')"
                class="px-6 py-3 bg-red-500 hover:bg-red-600 rounded-xl text-white font-semibold transition-colors">
            Hapus Akun
        </button>
    </div>

    <x-modal name="confirm-user-deletion" :show="$errors->userDeletion->isNotEmpty()" focusable>
        <div class="glass p-8 rounded-2xl">
            <form method="post" action="{{ route('profile.destroy') }}">
                @csrf
                @method('delete')

                <h2 class="text-2xl font-bold text-white mb-4">
                    Yakin ingin menghapus akun?
                </h2>

                <p class="text-gray-300 mb-6">
                    Setelah akun Anda dihapus, semua sumber daya dan data akan dihapus secara permanen. Masukkan password Anda untuk mengkonfirmasi penghapusan akun.
                </p>

                <div class="mb-6">
                    <input id="password"
                           name="password"
                           type="password"
                           placeholder="Password"
                           class="w-full px-4 py-3 bg-white/10 border border-white/20 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-red-500 @error('password', 'userDeletion') border-red-500 @enderror">
                    @error('password', 'userDeletion')
                        <p class="mt-1 text-sm text-red-400">{{ $message }}</p>
                    @enderror
                </div>

                <div class="flex justify-end space-x-4">
                    <button type="button"
                            x-on:click="$dispatch('close')"
                            class="px-6 py-3 bg-white/10 hover:bg-white/20 rounded-xl text-white font-semibold transition-colors border border-white/20">
                        Batal
                    </button>
                    <button type="submit"
                            class="px-6 py-3 bg-red-500 hover:bg-red-600 rounded-xl text-white font-semibold transition-colors">
                        Hapus Akun
                    </button>
                </div>
            </form>
        </div>
    </x-modal>
</section>
