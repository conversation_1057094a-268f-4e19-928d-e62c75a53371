<x-app-layout>
    <div class="py-8">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <!-- Welcome Section -->
            <div class="glass rounded-2xl p-8 mb-8" data-aos="fade-up">
                <div class="flex items-center justify-between">
                    <div>
                        <h1 class="text-3xl font-bold text-white mb-2">
                            Selamat datang, {{ auth()->user()->name }}! 👋
                        </h1>
                        <p class="text-gray-300">
                            Ke<PERSON>la destinasi impianmu dan wujudkan perjalanan yang tak terlupakan
                        </p>
                    </div>
                    <div class="hidden md:block">
                        <a href="{{ route('destinations.create') }}"
                           class="px-6 py-3 bg-gradient-to-r from-cyan-500 to-purple-500 rounded-xl text-white font-semibold hover:from-cyan-600 hover:to-purple-600 transform hover:scale-105 transition-all duration-300 shadow-lg">
                            + <PERSON><PERSON> Destinasi
                        </a>
                    </div>
                </div>
            </div>

            <!-- Statistics Cards -->
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
                <!-- Total Destinations -->
                <div class="glass rounded-2xl p-6 hover-lift" data-aos="fade-up" data-aos-delay="100">
                    <div class="flex items-center">
                        <div class="w-12 h-12 bg-gradient-to-r from-blue-500 to-cyan-500 rounded-xl flex items-center justify-center mr-4">
                            <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                            </svg>
                        </div>
                        <div>
                            <p class="text-gray-300 text-sm">Total Destinasi</p>
                            <p class="text-2xl font-bold text-white">{{ $totalDestinations }}</p>
                        </div>
                    </div>
                </div>

                <!-- Dream Destinations -->
                <div class="glass rounded-2xl p-6 hover-lift" data-aos="fade-up" data-aos-delay="200">
                    <div class="flex items-center">
                        <div class="w-12 h-12 bg-gradient-to-r from-purple-500 to-pink-500 rounded-xl flex items-center justify-center mr-4">
                            <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 3v4M3 5h4M6 17v4m-2-2h4m5-16l2.286 6.857L21 12l-5.714 2.143L13 21l-2.286-6.857L5 12l5.714-2.143L13 3z"></path>
                            </svg>
                        </div>
                        <div>
                            <p class="text-gray-300 text-sm">Destinasi Impian</p>
                            <p class="text-2xl font-bold text-white">{{ $dreamDestinations }}</p>
                        </div>
                    </div>
                </div>

                <!-- Visited Destinations -->
                <div class="glass rounded-2xl p-6 hover-lift" data-aos="fade-up" data-aos-delay="300">
                    <div class="flex items-center">
                        <div class="w-12 h-12 bg-gradient-to-r from-green-500 to-teal-500 rounded-xl flex items-center justify-center mr-4">
                            <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                            </svg>
                        </div>
                        <div>
                            <p class="text-gray-300 text-sm">Sudah Dikunjungi</p>
                            <p class="text-2xl font-bold text-white">{{ $visitedDestinations }}</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Charts Section -->
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
                <!-- Mood Statistics -->
                <div class="glass rounded-2xl p-6" data-aos="fade-up" data-aos-delay="400">
                    <h3 class="text-xl font-bold text-white mb-4">Statistik Mood</h3>
                    <div class="relative">
                        <canvas id="moodChart" width="400" height="200"></canvas>
                    </div>
                </div>

                <!-- Season Statistics -->
                <div class="glass rounded-2xl p-6" data-aos="fade-up" data-aos-delay="500">
                    <h3 class="text-xl font-bold text-white mb-4">Statistik Musim</h3>
                    <div class="relative">
                        <canvas id="seasonChart" width="400" height="200"></canvas>
                    </div>
                </div>
            </div>

            <!-- Recent Destinations -->
            <div class="glass rounded-2xl p-6" data-aos="fade-up" data-aos-delay="600">
                <div class="flex items-center justify-between mb-6">
                    <h3 class="text-xl font-bold text-white">Destinasi Terbaru</h3>
                    <a href="{{ route('destinations.index') }}"
                       class="text-cyan-400 hover:text-cyan-300 transition-colors">
                        Lihat Semua →
                    </a>
                </div>

                @if($recentDestinations->count() > 0)
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                        @foreach($recentDestinations as $destination)
                        <div class="bg-white/10 rounded-xl overflow-hidden hover-lift">
                            <div class="aspect-video bg-gradient-to-br from-cyan-500 to-purple-500 relative">
                                <img src="{{ $destination->image_url }}"
                                     alt="{{ $destination->nama_tempat }}"
                                     class="w-full h-full object-cover"
                                     loading="lazy">
                                <div class="absolute inset-0 bg-black/20"></div>
                                <div class="absolute top-3 right-3">
                                    <span class="px-2 py-1 bg-white/20 backdrop-blur-sm rounded-full text-white text-xs">
                                        {{ $destination->season_label }}
                                    </span>
                                </div>
                            </div>
                            <div class="p-4">
                                <h4 class="font-semibold text-white mb-1">{{ $destination->nama_tempat }}</h4>
                                <p class="text-gray-300 text-sm mb-2">{{ $destination->negara }}</p>
                                <div class="flex items-center justify-between">
                                    <span class="px-2 py-1 bg-gradient-to-r from-cyan-500/20 to-purple-500/20 rounded-full text-cyan-300 text-xs">
                                        {{ $destination->mood_label }}
                                    </span>
                                    <span class="text-xs {{ $destination->status ? 'text-green-400' : 'text-yellow-400' }}">
                                        {{ $destination->status_label }}
                                    </span>
                                </div>
                            </div>
                        </div>
                        @endforeach
                    </div>
                @else
                    <div class="text-center py-12">
                        <svg class="w-16 h-16 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                        </svg>
                        <h3 class="text-lg font-semibold text-gray-300 mb-2">Belum ada destinasi</h3>
                        <p class="text-gray-400 mb-4">Mulai tambahkan destinasi impianmu sekarang!</p>
                        <a href="{{ route('destinations.create') }}"
                           class="px-6 py-3 bg-gradient-to-r from-cyan-500 to-purple-500 rounded-xl text-white font-semibold hover:from-cyan-600 hover:to-purple-600 transform hover:scale-105 transition-all duration-300 shadow-lg">
                            + Tambah Destinasi Pertama
                        </a>
                    </div>
                @endif
            </div>
        </div>
    </div>

    <!-- Chart.js Scripts -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Mood Chart
            const moodCtx = document.getElementById('moodChart').getContext('2d');
            const moodData = @json($moodStats);
            const moodLabels = Object.keys(moodData);
            const moodValues = Object.values(moodData);

            new Chart(moodCtx, {
                type: 'doughnut',
                data: {
                    labels: moodLabels,
                    datasets: [{
                        data: moodValues,
                        backgroundColor: [
                            '#06b6d4', '#8b5cf6', '#ec4899', '#10b981',
                            '#f59e0b', '#ef4444', '#6366f1', '#84cc16'
                        ],
                        borderWidth: 0
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom',
                            labels: {
                                color: '#e5e7eb',
                                padding: 20
                            }
                        }
                    }
                }
            });

            // Season Chart
            const seasonCtx = document.getElementById('seasonChart').getContext('2d');
            const seasonData = @json($seasonStats);
            const seasonLabels = Object.keys(seasonData);
            const seasonValues = Object.values(seasonData);

            new Chart(seasonCtx, {
                type: 'bar',
                data: {
                    labels: seasonLabels,
                    datasets: [{
                        data: seasonValues,
                        backgroundColor: '#06b6d4',
                        borderRadius: 8,
                        borderSkipped: false,
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            ticks: {
                                color: '#e5e7eb'
                            },
                            grid: {
                                color: 'rgba(255, 255, 255, 0.1)'
                            }
                        },
                        x: {
                            ticks: {
                                color: '#e5e7eb'
                            },
                            grid: {
                                display: false
                            }
                        }
                    }
                }
            });
        });
    </script>
</x-app-layout>
