#!/bin/bash

# Dream Destinations Deployment Script
# This script prepares the application for production deployment

echo "🚀 Starting Dream Destinations Deployment..."

# Check if we're in the right directory
if [ ! -f "artisan" ]; then
    echo "❌ Error: artisan file not found. Please run this script from the Laravel root directory."
    exit 1
fi

# Install/Update Composer dependencies (production)
echo "📦 Installing Composer dependencies..."
composer install --optimize-autoloader --no-dev --no-interaction --prefer-dist

# Install/Update NPM dependencies
echo "📦 Installing NPM dependencies..."
npm ci --production

# Build assets for production
echo "🏗️ Building production assets..."
npm run build

# Clear all caches
echo "🧹 Clearing caches..."
php artisan cache:clear
php artisan config:clear
php artisan route:clear
php artisan view:clear

# Generate optimized autoloader
echo "⚡ Optimizing autoloader..."
composer dump-autoload --optimize

# Cache configuration for production
echo "⚡ Caching configuration..."
php artisan config:cache
php artisan route:cache
php artisan view:cache

# Run database migrations (if needed)
echo "🗄️ Running database migrations..."
php artisan migrate --force

# Create storage link if not exists
echo "🔗 Creating storage link..."
php artisan storage:link

# Set proper permissions
echo "🔒 Setting file permissions..."
chmod -R 755 storage bootstrap/cache
chmod -R 775 storage/app/public
chmod -R 775 storage/framework
chmod -R 775 storage/logs

# Security: Remove debug files
echo "🛡️ Removing debug files..."
find public -name "debug-*.php" -delete
find public -name "test-*.php" -delete

# Create .htaccess backup
echo "💾 Creating .htaccess backup..."
cp .htaccess .htaccess.backup

# Verify critical files exist
echo "✅ Verifying critical files..."
critical_files=(".env" "composer.json" "package.json" "artisan")
for file in "${critical_files[@]}"; do
    if [ ! -f "$file" ]; then
        echo "❌ Critical file missing: $file"
        exit 1
    fi
done

# Check environment
if grep -q "APP_ENV=local" .env; then
    echo "⚠️ WARNING: APP_ENV is set to 'local'. Please change to 'production' for live deployment."
fi

if grep -q "APP_DEBUG=true" .env; then
    echo "⚠️ WARNING: APP_DEBUG is set to 'true'. Please change to 'false' for live deployment."
fi

echo "✅ Deployment preparation completed successfully!"
echo ""
echo "📋 Next steps for Hostinger deployment:"
echo "1. Upload all files to your hosting directory"
echo "2. Update .env file with production settings"
echo "3. Set APP_ENV=production and APP_DEBUG=false"
echo "4. Configure database settings in .env"
echo "5. Generate new APP_KEY: php artisan key:generate"
echo "6. Run: php artisan migrate --force"
echo "7. Set proper file permissions on server"
echo ""
echo "🔒 Security checklist:"
echo "✅ Security headers middleware added"
echo "✅ .htaccess security rules configured"
echo "✅ Debug files removed"
echo "✅ Sensitive files protected"
echo "✅ Production optimizations applied"
echo ""
echo "🎉 Your Dream Destinations app is ready for production!"
