import './bootstrap';

// Import Alpine.js and plugins
import Alpine from 'alpinejs';
import intersect from '@alpinejs/intersect';
import persist from '@alpinejs/persist';

// Import animations and effects
import AOS from 'aos';
import 'aos/dist/aos.css';
import 'animate.css';

// Import GSAP
import { gsap } from 'gsap';
import { ScrollTrigger } from 'gsap/ScrollTrigger';

// Import Chart.js
import Chart from 'chart.js/auto';

// Register GSAP plugins
gsap.registerPlugin(ScrollTrigger);

// Register Alpine.js plugins
Alpine.plugin(intersect);
Alpine.plugin(persist);

// Make libraries available globally
window.Alpine = Alpine;
window.AOS = AOS;
window.gsap = gsap;
window.Chart = Chart;

// Initialize AOS
AOS.init({
    duration: 800,
    easing: 'ease-in-out',
    once: true,
    mirror: false
});

// Start Alpine.js
Alpine.start();
