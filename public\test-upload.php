<?php
// Test upload handler
echo "<h1>Upload Test Results</h1>";
echo "<style>body{font-family:Arial;margin:20px;} .success{color:green;} .error{color:red;}</style>";

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    echo "<h2>POST Data Analysis</h2>";
    echo "<p>Content Length: " . ($_SERVER['CONTENT_LENGTH'] ?? '0') . " bytes</p>";
    echo "<p>POST Max Size: " . ini_get('post_max_size') . "</p>";
    echo "<p>Upload Max Size: " . ini_get('upload_max_filesize') . "</p>";
    
    if (empty($_POST) && empty($_FILES) && $_SERVER['CONTENT_LENGTH'] > 0) {
        echo "<p class='error'>❌ POST data too large detected!</p>";
        echo "<p>Content Length: " . $_SERVER['CONTENT_LENGTH'] . " bytes</p>";
        echo "<p>POST Max Size: " . ini_get('post_max_size') . "</p>";
    } else {
        echo "<p class='success'>✅ POST data size OK</p>";
        
        if (isset($_FILES['test_file'])) {
            $file = $_FILES['test_file'];
            echo "<h3>File Info:</h3>";
            echo "<p>Name: " . $file['name'] . "</p>";
            echo "<p>Size: " . $file['size'] . " bytes (" . round($file['size']/1024/1024, 2) . " MB)</p>";
            echo "<p>Type: " . $file['type'] . "</p>";
            echo "<p>Error: " . $file['error'] . "</p>";
            
            if ($file['error'] === UPLOAD_ERR_OK) {
                echo "<p class='success'>✅ File upload successful!</p>";
            } else {
                echo "<p class='error'>❌ File upload error: " . $file['error'] . "</p>";
            }
        }
    }
} else {
    echo "<p>No POST data received</p>";
}

echo "<p><a href='test-config.php'>← Back to Config Test</a></p>";
echo "<p><strong>⚠️ DELETE THIS FILE AFTER TESTING!</strong></p>";
?>
