// Chunked Upload Implementation
// This provides a fallback for large file uploads

class ChunkedUpload {
    constructor(file, options = {}) {
        this.file = file;
        this.chunkSize = options.chunkSize || 1024 * 1024; // 1MB chunks
        this.totalChunks = Math.ceil(file.size / this.chunkSize);
        this.currentChunk = 0;
        this.uploadId = this.generateUploadId();
        this.onProgress = options.onProgress || (() => {});
        this.onComplete = options.onComplete || (() => {});
        this.onError = options.onError || (() => {});
    }

    generateUploadId() {
        return Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    }

    async upload() {
        try {
            for (let i = 0; i < this.totalChunks; i++) {
                await this.uploadChunk(i);
                this.currentChunk = i + 1;
                this.onProgress({
                    loaded: this.currentChunk * this.chunkSize,
                    total: this.file.size,
                    percentage: Math.round((this.currentChunk / this.totalChunks) * 100)
                });
            }

            // Finalize upload
            const result = await this.finalizeUpload();
            this.onComplete(result);
            return result;
        } catch (error) {
            this.onError(error);
            throw error;
        }
    }

    async uploadChunk(chunkIndex) {
        const start = chunkIndex * this.chunkSize;
        const end = Math.min(start + this.chunkSize, this.file.size);
        const chunk = this.file.slice(start, end);

        const formData = new FormData();
        formData.append('chunk', chunk);
        formData.append('chunkIndex', chunkIndex);
        formData.append('totalChunks', this.totalChunks);
        formData.append('uploadId', this.uploadId);
        formData.append('fileName', this.file.name);
        formData.append('fileSize', this.file.size);

        const response = await fetch('/api/upload-chunk', {
            method: 'POST',
            body: formData,
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            }
        });

        if (!response.ok) {
            throw new Error(`Chunk upload failed: ${response.statusText}`);
        }

        return response.json();
    }

    async finalizeUpload() {
        const response = await fetch('/api/finalize-upload', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            },
            body: JSON.stringify({
                uploadId: this.uploadId,
                fileName: this.file.name,
                fileSize: this.file.size,
                totalChunks: this.totalChunks
            })
        });

        if (!response.ok) {
            throw new Error(`Upload finalization failed: ${response.statusText}`);
        }

        return response.json();
    }
}

// Enhanced file upload with chunked fallback
function enhancedFileUpload(fileInput, options = {}) {
    const file = fileInput.files[0];
    if (!file) return;

    const maxDirectUploadSize = 5 * 1024 * 1024; // 5MB
    
    if (file.size > maxDirectUploadSize) {
        // Use chunked upload for large files
        console.log('Using chunked upload for large file:', file.name);
        
        const chunkedUpload = new ChunkedUpload(file, {
            onProgress: (progress) => {
                console.log(`Upload progress: ${progress.percentage}%`);
                if (options.onProgress) options.onProgress(progress);
            },
            onComplete: (result) => {
                console.log('Chunked upload completed:', result);
                if (options.onComplete) options.onComplete(result);
            },
            onError: (error) => {
                console.error('Chunked upload error:', error);
                if (options.onError) options.onError(error);
            }
        });

        return chunkedUpload.upload();
    } else {
        // Use direct upload for small files
        console.log('Using direct upload for small file:', file.name);
        return directUpload(file, options);
    }
}

async function directUpload(file, options = {}) {
    const formData = new FormData();
    formData.append('file', file);

    try {
        const response = await fetch('/api/direct-upload', {
            method: 'POST',
            body: formData,
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            }
        });

        if (!response.ok) {
            throw new Error(`Direct upload failed: ${response.statusText}`);
        }

        const result = await response.json();
        if (options.onComplete) options.onComplete(result);
        return result;
    } catch (error) {
        if (options.onError) options.onError(error);
        throw error;
    }
}

// Export for use in other files
window.ChunkedUpload = ChunkedUpload;
window.enhancedFileUpload = enhancedFileUpload;
