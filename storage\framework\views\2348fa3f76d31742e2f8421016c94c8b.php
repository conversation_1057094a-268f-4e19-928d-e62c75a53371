<?php if (isset($component)) { $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54 = $attributes; } ?>
<?php $component = App\View\Components\AppLayout::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('app-layout'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\App\View\Components\AppLayout::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
    <div class="py-8">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <!-- Welcome Section -->
            <div class="glass rounded-2xl p-8 mb-8" data-aos="fade-up">
                <div class="flex items-center justify-between">
                    <div>
                        <h1 class="text-3xl font-bold text-white mb-2">
                            Selamat datang, <?php echo e(auth()->user()->name); ?>! 👋
                        </h1>
                        <p class="text-gray-300">
                            Kelola destinasi impianmu dan wujudkan perjalanan yang tak terlupakan
                        </p>
                    </div>
                    <div class="hidden md:block">
                        <a href="<?php echo e(route('destinations.create')); ?>"
                           class="px-6 py-3 bg-gradient-to-r from-cyan-500 to-purple-500 rounded-xl text-white font-semibold hover:from-cyan-600 hover:to-purple-600 transform hover:scale-105 transition-all duration-300 shadow-lg">
                            + Tambah Destinasi
                        </a>
                    </div>
                </div>
            </div>

            <!-- Statistics Cards -->
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
                <!-- Total Destinations -->
                <div class="glass rounded-2xl p-6 hover-lift" data-aos="fade-up" data-aos-delay="100">
                    <div class="flex items-center">
                        <div class="w-12 h-12 bg-gradient-to-r from-blue-500 to-cyan-500 rounded-xl flex items-center justify-center mr-4">
                            <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                            </svg>
                        </div>
                        <div>
                            <p class="text-gray-300 text-sm">Total Destinasi</p>
                            <p class="text-2xl font-bold text-white"><?php echo e($totalDestinations); ?></p>
                        </div>
                    </div>
                </div>

                <!-- Dream Destinations -->
                <div class="glass rounded-2xl p-6 hover-lift" data-aos="fade-up" data-aos-delay="200">
                    <div class="flex items-center">
                        <div class="w-12 h-12 bg-gradient-to-r from-purple-500 to-pink-500 rounded-xl flex items-center justify-center mr-4">
                            <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 3v4M3 5h4M6 17v4m-2-2h4m5-16l2.286 6.857L21 12l-5.714 2.143L13 21l-2.286-6.857L5 12l5.714-2.143L13 3z"></path>
                            </svg>
                        </div>
                        <div>
                            <p class="text-gray-300 text-sm">Destinasi Impian</p>
                            <p class="text-2xl font-bold text-white"><?php echo e($dreamDestinations); ?></p>
                        </div>
                    </div>
                </div>

                <!-- Visited Destinations -->
                <div class="glass rounded-2xl p-6 hover-lift" data-aos="fade-up" data-aos-delay="300">
                    <div class="flex items-center">
                        <div class="w-12 h-12 bg-gradient-to-r from-green-500 to-teal-500 rounded-xl flex items-center justify-center mr-4">
                            <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                            </svg>
                        </div>
                        <div>
                            <p class="text-gray-300 text-sm">Sudah Dikunjungi</p>
                            <p class="text-2xl font-bold text-white"><?php echo e($visitedDestinations); ?></p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Charts Section -->
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
                <!-- Mood Statistics -->
                <div class="glass rounded-2xl p-6" data-aos="fade-up" data-aos-delay="400">
                    <h3 class="text-xl font-bold text-white mb-4">Statistik Mood</h3>
                    <div class="relative">
                        <canvas id="moodChart" width="400" height="200"></canvas>
                    </div>
                </div>

                <!-- Season Statistics -->
                <div class="glass rounded-2xl p-6" data-aos="fade-up" data-aos-delay="500">
                    <h3 class="text-xl font-bold text-white mb-4">Statistik Musim</h3>
                    <div class="relative">
                        <canvas id="seasonChart" width="400" height="200"></canvas>
                    </div>
                </div>
            </div>

            <!-- Recent Destinations -->
            <div class="glass rounded-2xl p-6" data-aos="fade-up" data-aos-delay="600">
                <div class="flex items-center justify-between mb-6">
                    <h3 class="text-xl font-bold text-white">Destinasi Terbaru</h3>
                    <a href="<?php echo e(route('destinations.index')); ?>"
                       class="text-cyan-400 hover:text-cyan-300 transition-colors">
                        Lihat Semua →
                    </a>
                </div>

                <?php if($recentDestinations->count() > 0): ?>
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                        <?php $__currentLoopData = $recentDestinations; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $destination): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <div class="bg-white/10 rounded-xl overflow-hidden hover-lift">
                            <div class="aspect-video bg-gradient-to-br from-cyan-500 to-purple-500 relative">
                                <img src="<?php echo e($destination->image_url); ?>"
                                     alt="<?php echo e($destination->nama_tempat); ?>"
                                     class="w-full h-full object-cover"
                                     loading="lazy">
                                <div class="absolute inset-0 bg-black/20"></div>
                                <div class="absolute top-3 right-3">
                                    <span class="px-2 py-1 bg-white/20 backdrop-blur-sm rounded-full text-white text-xs">
                                        <?php echo e($destination->season_label); ?>

                                    </span>
                                </div>
                            </div>
                            <div class="p-4">
                                <h4 class="font-semibold text-white mb-1"><?php echo e($destination->nama_tempat); ?></h4>
                                <p class="text-gray-300 text-sm mb-2"><?php echo e($destination->negara); ?></p>
                                <div class="flex items-center justify-between">
                                    <span class="px-2 py-1 bg-gradient-to-r from-cyan-500/20 to-purple-500/20 rounded-full text-cyan-300 text-xs">
                                        <?php echo e($destination->mood_label); ?>

                                    </span>
                                    <span class="text-xs <?php echo e($destination->status ? 'text-green-400' : 'text-yellow-400'); ?>">
                                        <?php echo e($destination->status_label); ?>

                                    </span>
                                </div>
                            </div>
                        </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </div>
                <?php else: ?>
                    <div class="text-center py-12">
                        <svg class="w-16 h-16 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                        </svg>
                        <h3 class="text-lg font-semibold text-gray-300 mb-2">Belum ada destinasi</h3>
                        <p class="text-gray-400 mb-4">Mulai tambahkan destinasi impianmu sekarang!</p>
                        <a href="<?php echo e(route('destinations.create')); ?>"
                           class="px-6 py-3 bg-gradient-to-r from-cyan-500 to-purple-500 rounded-xl text-white font-semibold hover:from-cyan-600 hover:to-purple-600 transform hover:scale-105 transition-all duration-300 shadow-lg">
                            + Tambah Destinasi Pertama
                        </a>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- Chart.js Scripts -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Mood Chart
            const moodCtx = document.getElementById('moodChart').getContext('2d');
            const moodData = <?php echo json_encode($moodStats, 15, 512) ?>;
            const moodLabels = Object.keys(moodData);
            const moodValues = Object.values(moodData);

            new Chart(moodCtx, {
                type: 'doughnut',
                data: {
                    labels: moodLabels,
                    datasets: [{
                        data: moodValues,
                        backgroundColor: [
                            '#06b6d4', '#8b5cf6', '#ec4899', '#10b981',
                            '#f59e0b', '#ef4444', '#6366f1', '#84cc16'
                        ],
                        borderWidth: 0
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom',
                            labels: {
                                color: '#e5e7eb',
                                padding: 20
                            }
                        }
                    }
                }
            });

            // Season Chart
            const seasonCtx = document.getElementById('seasonChart').getContext('2d');
            const seasonData = <?php echo json_encode($seasonStats, 15, 512) ?>;
            const seasonLabels = Object.keys(seasonData);
            const seasonValues = Object.values(seasonData);

            new Chart(seasonCtx, {
                type: 'bar',
                data: {
                    labels: seasonLabels,
                    datasets: [{
                        data: seasonValues,
                        backgroundColor: '#06b6d4',
                        borderRadius: 8,
                        borderSkipped: false,
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            ticks: {
                                color: '#e5e7eb'
                            },
                            grid: {
                                color: 'rgba(255, 255, 255, 0.1)'
                            }
                        },
                        x: {
                            ticks: {
                                color: '#e5e7eb'
                            },
                            grid: {
                                display: false
                            }
                        }
                    }
                }
            });
        });
    </script>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $attributes = $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $component = $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php /**PATH C:\Users\<USER>\Herd\abdulsomadm\resources\views/dashboard.blade.php ENDPATH**/ ?>