; PHP Configuration for File Uploads - Aggressive Settings
; This file will be read by PHP-FPM and Apache

; File upload settings - Very generous limits
upload_max_filesize = 50M
post_max_size = 60M
max_file_uploads = 50

; Execution settings - Extended timeouts
max_execution_time = 600
max_input_time = 600
memory_limit = 1024M

; Input settings - High limits
max_input_vars = 10000
max_input_nesting_level = 128

; Buffer settings
output_buffering = Off
implicit_flush = On

; Other critical settings
file_uploads = On
allow_url_fopen = On
auto_detect_line_endings = On

; Error handling
log_errors = On
display_errors = Off

; Session settings
session.upload_progress.enabled = On
session.upload_progress.cleanup = On
