<x-app-layout>
    <div class="py-8">
        <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
            <!-- Header -->
            <div class="glass rounded-2xl p-8 mb-8" data-aos="fade-up">
                <div class="flex items-center space-x-4">
                    <a href="{{ route('destinations.index') }}" 
                       class="p-2 glass rounded-lg hover:bg-white/20 transition-colors">
                        <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                        </svg>
                    </a>
                    <div>
                        <h1 class="text-3xl font-bold text-white mb-2">Tambah Destinasi Baru</h1>
                        <p class="text-gray-300">Catat destinasi impianmu dan wujudkan perjalanan yang tak terlupakan</p>
                    </div>
                </div>
            </div>

            <!-- Form -->
            <div class="glass rounded-2xl p-8" data-aos="fade-up" data-aos-delay="100">
                <form action="{{ route('destinations.store') }}" method="POST" enctype="multipart/form-data" class="space-y-6">
                    @csrf

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <!-- Nama Tempat -->
                        <div>
                            <label for="nama_tempat" class="block text-sm font-medium text-white mb-2">
                                Nama Tempat *
                            </label>
                            <input type="text"
                                   id="nama_tempat"
                                   name="nama_tempat"
                                   value="{{ old('nama_tempat') }}"
                                   placeholder="Contoh: Borobudur, Kyoto, Santorini"
                                   class="w-full px-4 py-3 bg-gradient-to-r from-slate-800/90 to-slate-700/90 border border-cyan-500/30 rounded-lg text-white placeholder-gray-300 focus:outline-none focus:ring-2 focus:ring-cyan-400 focus:border-cyan-400 transition-all duration-300 backdrop-blur-sm @error('nama_tempat') border-red-500 @enderror"
                                   required>
                            @error('nama_tempat')
                                <p class="mt-1 text-sm text-red-400">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Negara -->
                        <div>
                            <label for="negara" class="block text-sm font-medium text-white mb-2">
                                🌍 Negara *
                            </label>
                            <input type="text"
                                   id="negara"
                                   name="negara"
                                   value="{{ old('negara') }}"
                                   placeholder="Contoh: Indonesia, Jepang, Yunani"
                                   class="w-full px-4 py-3 bg-gradient-to-r from-slate-800/90 to-slate-700/90 border border-emerald-500/30 rounded-xl text-white placeholder-gray-300 focus:outline-none focus:ring-2 focus:ring-emerald-400 focus:border-emerald-400 transition-all duration-300 backdrop-blur-sm @error('negara') border-red-500 @enderror"
                                   required>
                            @error('negara')
                                <p class="mt-1 text-sm text-red-400">{{ $message }}</p>
                            @enderror
                        </div>
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <!-- Musim -->
                        <div class="relative">
                            <label for="musim" class="block text-sm font-medium text-white mb-2">
                                🌤️ Musim Terbaik *
                            </label>
                            <div class="relative">
                                <select id="musim"
                                        name="musim"
                                        style="background: linear-gradient(135deg, rgba(30, 41, 59, 0.95), rgba(51, 65, 85, 0.95)); color: #ffffff; border: 1px solid rgba(6, 182, 212, 0.3);"
                                        class="w-full px-4 py-3 rounded-xl text-white focus:outline-none focus:ring-2 focus:ring-cyan-400 focus:border-cyan-400 transition-all duration-300 backdrop-blur-sm appearance-none cursor-pointer @error('musim') border-red-500 @enderror"
                                        required>
                                    <option value="" style="background: #1e293b; color: #ffffff;">🌍 Pilih Musim Terbaik</option>
                                    @foreach(\App\Models\Destination::$seasons as $key => $label)
                                        <option value="{{ $key }}" {{ old('musim') == $key ? 'selected' : '' }} style="background: #1e293b; color: #ffffff; padding: 8px;">{{ $label }}</option>
                                    @endforeach
                                </select>
                                <div class="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                                    <svg class="w-5 h-5 text-cyan-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                    </svg>
                                </div>
                            </div>
                            @error('musim')
                                <p class="mt-1 text-sm text-red-400">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Mood -->
                        <div class="relative">
                            <label for="mood" class="block text-sm font-medium text-white mb-2">
                                😊 Mood Perjalanan *
                            </label>
                            <div class="relative">
                                <select id="mood"
                                        name="mood"
                                        style="background: linear-gradient(135deg, rgba(30, 41, 59, 0.95), rgba(51, 65, 85, 0.95)); color: #ffffff; border: 1px solid rgba(139, 92, 246, 0.3);"
                                        class="w-full px-4 py-3 rounded-xl text-white focus:outline-none focus:ring-2 focus:ring-purple-400 focus:border-purple-400 transition-all duration-300 backdrop-blur-sm appearance-none cursor-pointer @error('mood') border-red-500 @enderror"
                                        required>
                                    <option value="" style="background: #1e293b; color: #ffffff;">💭 Pilih Mood Perjalanan</option>
                                    @foreach(\App\Models\Destination::$moods as $key => $label)
                                        <option value="{{ $key }}" {{ old('mood') == $key ? 'selected' : '' }} style="background: #1e293b; color: #ffffff; padding: 8px;">{{ $label }}</option>
                                    @endforeach
                                </select>
                                <div class="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                                    <svg class="w-5 h-5 text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                    </svg>
                                </div>
                            </div>
                            @error('mood')
                                <p class="mt-1 text-sm text-red-400">{{ $message }}</p>
                            @enderror
                        </div>
                    </div>

                    <!-- Status -->
                    <div>
                        <label class="flex items-center space-x-3">
                            <input type="checkbox" 
                                   name="status" 
                                   value="1"
                                   {{ old('status') ? 'checked' : '' }}
                                   class="w-5 h-5 text-cyan-500 bg-white/10 border-white/20 rounded focus:ring-cyan-500 focus:ring-2">
                            <span class="text-white">Sudah dikunjungi</span>
                        </label>
                        <p class="mt-1 text-sm text-gray-400">Centang jika kamu sudah pernah mengunjungi destinasi ini</p>
                    </div>

                    <!-- Deskripsi -->
                    <div>
                        <label for="deskripsi" class="block text-sm font-medium text-white mb-2">
                            Deskripsi *
                        </label>
                        <textarea id="deskripsi" 
                                  name="deskripsi" 
                                  rows="4"
                                  placeholder="Ceritakan tentang destinasi ini, mengapa kamu ingin mengunjunginya, atau pengalamanmu jika sudah pernah ke sana..."
                                  class="w-full px-4 py-3 bg-white/10 border border-white/20 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-cyan-500 @error('deskripsi') border-red-500 @enderror"
                                  required>{{ old('deskripsi') }}</textarea>
                        @error('deskripsi')
                            <p class="mt-1 text-sm text-red-400">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Upload Gambar -->
                    <div>
                        <label for="gambar" class="block text-sm font-medium text-white mb-2">
                            📸 Upload Gambar (Opsional)
                        </label>
                        <div class="relative">
                            <input type="file"
                                   id="gambar"
                                   name="gambar"
                                   accept="image/jpeg,image/jpg,image/png,image/gif,image/webp"
                                   class="w-full px-4 py-3 bg-gradient-to-r from-slate-800/90 to-slate-700/90 border border-orange-500/30 rounded-xl text-white file:mr-4 file:py-2 file:px-4 file:rounded-lg file:border-0 file:bg-gradient-to-r file:from-orange-500 file:to-red-500 file:text-white file:cursor-pointer hover:file:from-orange-600 hover:file:to-red-600 focus:outline-none focus:ring-2 focus:ring-orange-400 focus:border-orange-400 transition-all duration-300 backdrop-blur-sm @error('gambar') border-red-500 @enderror"
                                   onchange="validateFileSize(this)">
                            <div class="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                                <svg class="w-5 h-5 text-orange-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"></path>
                                </svg>
                            </div>
                        </div>

                        <!-- File Info -->
                        <div class="mt-2 flex items-center justify-between text-sm">
                            <p class="text-gray-400">
                                Format: JPG, PNG, GIF, WEBP • Maksimal: 10MB
                            </p>
                            <div id="file-info" class="text-cyan-400 hidden"></div>
                        </div>

                        <!-- Error Display -->
                        <div id="file-size-error" class="mt-2 text-sm text-red-400 hidden"></div>
                        @error('gambar')
                            <p class="mt-2 text-sm text-red-400">{{ $message }}</p>
                        @enderror

                        <!-- Image Preview -->
                        <div id="imagePreview" class="mt-4 hidden">
                            <p class="text-sm text-gray-400 mb-2">Preview:</p>
                            <div class="relative">
                                <img id="previewImg" src="" alt="Preview" class="w-full max-w-md h-48 object-cover rounded-lg shadow-lg border border-white/20">
                                <button type="button" onclick="clearImagePreview()" class="absolute top-2 right-2 bg-red-500 hover:bg-red-600 text-white rounded-full w-6 h-6 flex items-center justify-center text-xs transition-colors">
                                    ×
                                </button>
                            </div>
                        </div>

                        <!-- Fallback Info -->
                        <div class="mt-3 p-3 bg-blue-500/10 border border-blue-500/20 rounded-lg">
                            <p class="text-blue-300 text-sm">
                                💡 <strong>Tips:</strong> Jika tidak upload gambar, sistem akan otomatis mengambil gambar dari Unsplash berdasarkan nama tempat.
                            </p>
                        </div>
                    </div>

                    <!-- Submit Buttons -->
                    <div class="flex flex-col sm:flex-row gap-4 pt-6">
                        <button type="submit" 
                                class="flex-1 px-6 py-3 bg-gradient-to-r from-cyan-500 to-purple-500 rounded-xl text-white font-semibold hover:from-cyan-600 hover:to-purple-600 transform hover:scale-105 transition-all duration-300 shadow-lg">
                            Simpan Destinasi
                        </button>
                        <a href="{{ route('destinations.index') }}" 
                           class="flex-1 px-6 py-3 bg-white/10 hover:bg-white/20 rounded-xl text-white font-semibold text-center transition-colors border border-white/20">
                            Batal
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Enhanced File Upload Script -->
    <script>
        // File size validation function
        function validateFileSize(input) {
            const file = input.files[0];
            const errorDiv = document.getElementById('file-size-error');
            const fileInfo = document.getElementById('file-info');
            const maxSize = 10 * 1024 * 1024; // 10MB in bytes

            // Clear previous states
            errorDiv.classList.add('hidden');
            fileInfo.classList.add('hidden');

            if (file) {
                const fileSize = file.size;
                const fileSizeMB = (fileSize / 1024 / 1024).toFixed(2);

                // Check file size
                if (fileSize > maxSize) {
                    errorDiv.textContent = `❌ File terlalu besar (${fileSizeMB}MB). Maksimal 10MB.`;
                    errorDiv.classList.remove('hidden');
                    input.value = ''; // Clear the input
                    clearImagePreview();
                    return false;
                }

                // Check file type
                const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
                if (!allowedTypes.includes(file.type)) {
                    errorDiv.textContent = '❌ Format file tidak didukung. Gunakan JPG, PNG, GIF, atau WEBP.';
                    errorDiv.classList.remove('hidden');
                    input.value = '';
                    clearImagePreview();
                    return false;
                }

                // Show file info
                fileInfo.textContent = `✅ ${file.name} (${fileSizeMB}MB)`;
                fileInfo.classList.remove('hidden');

                // Show preview
                const reader = new FileReader();
                reader.onload = function(e) {
                    document.getElementById('previewImg').src = e.target.result;
                    document.getElementById('imagePreview').classList.remove('hidden');
                }
                reader.readAsDataURL(file);
                return true;
            } else {
                clearImagePreview();
            }
        }

        // Clear image preview function
        function clearImagePreview() {
            document.getElementById('imagePreview').classList.add('hidden');
            document.getElementById('gambar').value = '';
            document.getElementById('file-info').classList.add('hidden');
            document.getElementById('file-size-error').classList.add('hidden');
        }

        // Form submission validation and feedback
        document.querySelector('form').addEventListener('submit', function(e) {
            const fileInput = document.getElementById('gambar');
            const submitBtn = document.querySelector('button[type="submit"]');

            // Final file validation before submit
            if (fileInput.files[0]) {
                const file = fileInput.files[0];
                const maxSize = 10 * 1024 * 1024; // 10MB

                if (file.size > maxSize) {
                    e.preventDefault();
                    alert('❌ Ukuran file terlalu besar. Maksimal 10MB.');
                    return false;
                }

                // Check if file is too large for server
                const serverMaxSize = 50 * 1024 * 1024; // 50MB server limit
                if (file.size > serverMaxSize) {
                    e.preventDefault();
                    alert('❌ File terlalu besar untuk server. Silakan kompres gambar terlebih dahulu.');
                    return false;
                }

                // Warn for large files
                if (file.size > 5 * 1024 * 1024) { // 5MB
                    const proceed = confirm('⚠️ File cukup besar (' + (file.size / 1024 / 1024).toFixed(2) + 'MB). Upload mungkin memakan waktu. Lanjutkan?');
                    if (!proceed) {
                        e.preventDefault();
                        return false;
                    }
                }
            }

            // Show loading state
            if (submitBtn) {
                submitBtn.disabled = true;
                const originalText = submitBtn.innerHTML;
                submitBtn.innerHTML = '<svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"><circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle><path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path></svg>Menyimpan...';

                // Timeout fallback
                setTimeout(() => {
                    if (submitBtn.disabled) {
                        submitBtn.disabled = false;
                        submitBtn.innerHTML = originalText;
                        alert('⚠️ Upload memakan waktu lama. Silakan coba lagi atau gunakan file yang lebih kecil.');
                    }
                }, 120000); // 2 minutes timeout
            }
        });

        // Drag and drop functionality
        const fileInput = document.getElementById('gambar');
        const fileInputContainer = fileInput.parentElement;

        ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
            fileInputContainer.addEventListener(eventName, preventDefaults, false);
        });

        function preventDefaults(e) {
            e.preventDefault();
            e.stopPropagation();
        }

        ['dragenter', 'dragover'].forEach(eventName => {
            fileInputContainer.addEventListener(eventName, highlight, false);
        });

        ['dragleave', 'drop'].forEach(eventName => {
            fileInputContainer.addEventListener(eventName, unhighlight, false);
        });

        function highlight(e) {
            fileInputContainer.classList.add('border-orange-400', 'bg-orange-500/10');
        }

        function unhighlight(e) {
            fileInputContainer.classList.remove('border-orange-400', 'bg-orange-500/10');
        }

        fileInputContainer.addEventListener('drop', handleDrop, false);

        function handleDrop(e) {
            const dt = e.dataTransfer;
            const files = dt.files;

            if (files.length > 0) {
                fileInput.files = files;
                validateFileSize(fileInput);
            }
        }
    </script>
</x-app-layout>
