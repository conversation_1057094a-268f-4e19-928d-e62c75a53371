#!/bin/bash

# Dream Destinations - Production Deployment Script
# This script prepares the application for production deployment

echo "🚀 Starting Dream Destinations deployment preparation..."

# Check if we're in the right directory
if [ ! -f "artisan" ]; then
    echo "❌ Error: artisan file not found. Please run this script from the Laravel root directory."
    exit 1
fi

# Check if .env exists
if [ ! -f ".env" ]; then
    echo "📋 Creating .env file from .env.example..."
    cp .env.example .env
    echo "⚠️  Please configure your .env file before continuing."
    exit 1
fi

echo "📦 Installing PHP dependencies..."
composer install --no-dev --optimize-autoloader

echo "📦 Installing Node.js dependencies..."
npm ci

echo "🔑 Generating application key..."
php artisan key:generate --force

echo "🗄️  Running database migrations..."
php artisan migrate --force

echo "🌱 Seeding database..."
php artisan db:seed --force

echo "🏗️  Building frontend assets..."
npm run build

echo "⚡ Optimizing application..."
php artisan config:cache
php artisan route:cache
php artisan view:cache

echo "🧹 Clearing old caches..."
php artisan cache:clear

echo "📁 Setting proper permissions..."
chmod -R 755 storage
chmod -R 755 bootstrap/cache

echo "✅ Deployment preparation completed!"
echo ""
echo "📋 Next steps:"
echo "1. Configure your .env file for production"
echo "2. Set APP_ENV=production"
echo "3. Set APP_DEBUG=false"
echo "4. Configure your database connection"
echo "5. Deploy to your hosting platform"
echo ""
echo "🌐 Recommended hosting platforms:"
echo "- Railway: https://railway.app"
echo "- Vercel: https://vercel.com"
echo "- Netlify: https://netlify.com"
echo "- Heroku: https://heroku.com"
echo ""
echo "📖 See DEPLOYMENT.md for detailed instructions"
