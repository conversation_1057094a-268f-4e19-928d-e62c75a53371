<?php

namespace App\Models;

// use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;

class User extends Authenticatable
{
    /** @use HasFactory<\Database\Factories\UserFactory> */
    use HasFactory, Notifiable;

    /**
     * The attributes that are mass assignable.
     *
     * @var list<string>
     */
    protected $fillable = [
        'name',
        'email',
        'password',
        'profile_photo',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var list<string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'email_verified_at' => 'datetime',
            'password' => 'hashed',
        ];
    }

    /**
     * Get the destinations for the user.
     */
    public function destinations(): HasMany
    {
        return $this->hasMany(Destination::class);
    }

    /**
     * Get the user's dream destinations.
     */
    public function dreamDestinations(): HasMany
    {
        return $this->hasMany(Destination::class)->where('status', false);
    }

    /**
     * Get the user's visited destinations.
     */
    public function visitedDestinations(): HasMany
    {
        return $this->hasMany(Destination::class)->where('status', true);
    }

    /**
     * Get the profile photo URL.
     */
    public function getProfilePhotoUrlAttribute(): string
    {
        if ($this->profile_photo && \Storage::disk('public')->exists('profile_photos/' . $this->profile_photo)) {
            return \Storage::url('profile_photos/' . $this->profile_photo);
        }

        // Default avatar with user initial
        return 'https://ui-avatars.com/api/?name=' . urlencode($this->name) . '&color=ffffff&background=06b6d4&size=200';
    }
}
