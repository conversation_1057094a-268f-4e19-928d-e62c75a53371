# Root .htaccess for PHP configuration and Security
# This applies to the entire application

# Security Headers
<IfModule mod_headers.c>
    # Prevent MIME type sniffing
    Header always set X-Content-Type-Options nosniff

    # Enable XSS protection
    Header always set X-XSS-Protection "1; mode=block"

    # Prevent clickjacking
    Header always set X-Frame-Options DENY

    # Referrer Policy
    Header always set Referrer-Policy "strict-origin-when-cross-origin"

    # Remove server signature
    Header unset Server
    Header unset X-Powered-By
</IfModule>

# Hide sensitive files
<FilesMatch "^\.">
    Order allow,deny
    Deny from all
</FilesMatch>

<FilesMatch "\.(env|log|ini|conf|sql|bak|backup|old|json|lock)$">
    Order allow,deny
    Deny from all
</FilesMatch>

# Protect Laravel directories
RedirectMatch 403 ^/storage/.*$
RedirectMatch 403 ^/bootstrap/.*$
RedirectMatch 403 ^/config/.*$
RedirectMatch 403 ^/database/.*$
RedirectMatch 403 ^/resources/.*$
RedirectMatch 403 ^/routes/.*$
RedirectMatch 403 ^/vendor/.*$
RedirectMatch 403 ^/tests/.*$
RedirectMatch 403 ^/app/.*$

# Disable directory browsing
Options -Indexes

# Aggressive PHP settings for file uploads
php_value upload_max_filesize 50M
php_value post_max_size 60M
php_value max_execution_time 600
php_value max_input_time 600
php_value memory_limit 1024M
php_value max_input_vars 10000
php_value max_file_uploads 50
php_admin_value file_uploads On

# Buffer settings
php_value output_buffering Off
php_value implicit_flush On

# Session settings
php_value session.upload_progress.enabled On
php_value session.upload_progress.cleanup On

# Error handling (Production)
php_value log_errors On
php_value display_errors Off
php_value display_startup_errors Off

# Security
php_value expose_php Off
php_value allow_url_include Off
php_value allow_url_fopen Off

# Performance
php_value realpath_cache_size 4096K
php_value realpath_cache_ttl 600

# Prevent access to debug files
<FilesMatch "^(debug|test)-.*\.php$">
    Order allow,deny
    Deny from all
</FilesMatch>

# Performance optimizations
<IfModule mod_expires.c>
    ExpiresActive On
    ExpiresByType text/css "access plus 1 year"
    ExpiresByType application/javascript "access plus 1 year"
    ExpiresByType image/png "access plus 1 year"
    ExpiresByType image/jpg "access plus 1 year"
    ExpiresByType image/jpeg "access plus 1 year"
    ExpiresByType image/gif "access plus 1 year"
    ExpiresByType image/webp "access plus 1 year"
</IfModule>

# Compression
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/javascript
</IfModule>

# Redirect to public directory
RewriteEngine On
RewriteCond %{REQUEST_URI} !^/public/
RewriteRule ^(.*)$ /public/$1 [L,QSA]
