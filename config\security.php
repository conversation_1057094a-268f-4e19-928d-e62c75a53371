<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Security Configuration
    |--------------------------------------------------------------------------
    |
    | This file contains security-related configuration options for the
    | Dream Destinations application.
    |
    */

    /*
    |--------------------------------------------------------------------------
    | Content Security Policy
    |--------------------------------------------------------------------------
    |
    | Define the Content Security Policy directives for the application.
    |
    */

    'csp' => [
        'default-src' => "'self'",
        'script-src' => "'self' 'unsafe-inline' 'unsafe-eval' https://unpkg.com https://cdn.jsdelivr.net",
        'style-src' => "'self' 'unsafe-inline' https://fonts.googleapis.com",
        'font-src' => "'self' https://fonts.gstatic.com",
        'img-src' => "'self' data: https: blob:",
        'connect-src' => "'self' https:",
        'media-src' => "'self'",
        'object-src' => "'none'",
        'frame-src' => "'none'",
    ],

    /*
    |--------------------------------------------------------------------------
    | Security Headers
    |--------------------------------------------------------------------------
    |
    | Configure security headers that should be sent with every response.
    |
    */

    'headers' => [
        'X-Content-Type-Options' => 'nosniff',
        'X-Frame-Options' => 'DENY',
        'X-XSS-Protection' => '1; mode=block',
        'Referrer-Policy' => 'strict-origin-when-cross-origin',
        'Permissions-Policy' => 'geolocation=(), microphone=(), camera=()',
    ],

    /*
    |--------------------------------------------------------------------------
    | File Upload Security
    |--------------------------------------------------------------------------
    |
    | Configure security settings for file uploads.
    |
    */

    'uploads' => [
        'max_size' => 10 * 1024 * 1024, // 10MB
        'allowed_types' => ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'],
        'allowed_extensions' => ['jpg', 'jpeg', 'png', 'gif', 'webp'],
        'scan_for_malware' => env('SCAN_UPLOADS_FOR_MALWARE', false),
        'quarantine_suspicious' => env('QUARANTINE_SUSPICIOUS_FILES', true),
    ],

    /*
    |--------------------------------------------------------------------------
    | Rate Limiting
    |--------------------------------------------------------------------------
    |
    | Configure rate limiting for various endpoints.
    |
    */

    'rate_limiting' => [
        'login' => '5,1', // 5 attempts per minute
        'register' => '3,1', // 3 attempts per minute
        'upload' => '10,1', // 10 uploads per minute
        'api' => '60,1', // 60 requests per minute
    ],

    /*
    |--------------------------------------------------------------------------
    | Session Security
    |--------------------------------------------------------------------------
    |
    | Configure session security settings.
    |
    */

    'session' => [
        'secure' => env('SESSION_SECURE_COOKIE', env('APP_ENV') === 'production'),
        'http_only' => true,
        'same_site' => 'strict',
        'lifetime' => 120, // 2 hours
    ],

    /*
    |--------------------------------------------------------------------------
    | Database Security
    |--------------------------------------------------------------------------
    |
    | Configure database security settings.
    |
    */

    'database' => [
        'encrypt_sensitive_data' => env('ENCRYPT_SENSITIVE_DATA', true),
        'log_queries' => env('LOG_DATABASE_QUERIES', false),
        'prevent_sql_injection' => true,
    ],

    /*
    |--------------------------------------------------------------------------
    | Logging Security
    |--------------------------------------------------------------------------
    |
    | Configure what should be logged for security purposes.
    |
    */

    'logging' => [
        'log_failed_logins' => true,
        'log_file_uploads' => true,
        'log_suspicious_activity' => true,
        'log_admin_actions' => true,
    ],

];
