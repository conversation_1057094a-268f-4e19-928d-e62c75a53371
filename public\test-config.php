<?php
// Test current PHP configuration
echo "<h1>Current PHP Configuration</h1>";
echo "<style>body{font-family:Arial;margin:20px;} table{border-collapse:collapse;width:100%;} th,td{border:1px solid #ddd;padding:8px;} th{background:#f2f2f2;} .good{color:green;} .bad{color:red;}</style>";

echo "<h2>Upload Settings</h2>";
echo "<table>";
echo "<tr><th>Setting</th><th>Value</th><th>Status</th></tr>";

$settings = [
    'upload_max_filesize' => ini_get('upload_max_filesize'),
    'post_max_size' => ini_get('post_max_size'),
    'memory_limit' => ini_get('memory_limit'),
    'max_execution_time' => ini_get('max_execution_time'),
    'max_input_time' => ini_get('max_input_time'),
    'max_input_vars' => ini_get('max_input_vars'),
    'file_uploads' => ini_get('file_uploads') ? 'On' : 'Off',
];

foreach ($settings as $key => $value) {
    $status = 'good';
    if ($key === 'upload_max_filesize' && (int)$value < 50) $status = 'bad';
    if ($key === 'post_max_size' && (int)$value < 60) $status = 'bad';
    if ($key === 'memory_limit' && (int)$value < 1024) $status = 'bad';
    if ($key === 'file_uploads' && $value !== 'On') $status = 'bad';
    
    echo "<tr><td>{$key}</td><td class='{$status}'>{$value}</td><td class='{$status}'>" . ucfirst($status) . "</td></tr>";
}

echo "</table>";

echo "<h2>Request Info</h2>";
echo "<table>";
echo "<tr><th>Item</th><th>Value</th></tr>";
echo "<tr><td>Content Length</td><td>" . ($_SERVER['CONTENT_LENGTH'] ?? '0') . "</td></tr>";
echo "<tr><td>Request Method</td><td>" . ($_SERVER['REQUEST_METHOD'] ?? 'GET') . "</td></tr>";
echo "<tr><td>Server Software</td><td>" . ($_SERVER['SERVER_SOFTWARE'] ?? 'Unknown') . "</td></tr>";
echo "<tr><td>PHP Version</td><td>" . PHP_VERSION . "</td></tr>";
echo "<tr><td>PHP SAPI</td><td>" . php_sapi_name() . "</td></tr>";
echo "</table>";

echo "<h2>Test Form</h2>";
echo "<form method='POST' enctype='multipart/form-data' action='test-upload.php'>";
echo "<input type='file' name='test_file' accept='image/*'><br><br>";
echo "<input type='submit' value='Test Upload'>";
echo "</form>";

echo "<p><strong>⚠️ DELETE THIS FILE AFTER TESTING!</strong></p>";
?>
