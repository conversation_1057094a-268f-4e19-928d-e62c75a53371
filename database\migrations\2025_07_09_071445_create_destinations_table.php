<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('destinations', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->string('nama_tempat');
            $table->string('negara');
            $table->enum('musim', ['spring', 'summer', 'autumn', 'winter']);
            $table->enum('mood', ['happy', 'healing', 'romantic', 'adventure', 'peaceful', 'exciting', 'cultural', 'nature']);
            $table->boolean('status')->default(0)->comment('0 = impian, 1 = sudah dikunjungi');
            $table->text('deskripsi');
            $table->string('gambar')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('destinations');
    }
};
