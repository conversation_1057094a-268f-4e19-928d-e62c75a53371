<x-app-layout>
    <div class="py-8">
        <div class="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
            <!-- Back Button -->
            <div class="mb-8" data-aos="fade-up">
                <a href="{{ route('destinations.index') }}" 
                   class="inline-flex items-center space-x-2 glass px-4 py-2 rounded-lg hover:bg-white/20 transition-colors">
                    <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                    </svg>
                    <span class="text-white">Ke<PERSON><PERSON> ke Daftar</span>
                </a>
            </div>

            <!-- Main Content -->
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                <!-- Image Section -->
                <div class="glass rounded-2xl overflow-hidden" data-aos="fade-up">
                    <div class="aspect-video bg-gradient-to-br from-cyan-500 to-purple-500 relative">
                        <img src="{{ $destination->image_url }}"
                             alt="{{ $destination->nama_tempat }}"
                             class="w-full h-full object-cover"
                             onerror="this.onerror=null; this.src='https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=800&h=600&fit=crop&crop=center';"
                             onload="this.style.opacity='1';"
                             style="opacity:0; transition: opacity 0.3s ease;">
                        <div class="absolute inset-0 bg-black/20"></div>
                        
                        <!-- Status Badge -->
                        <div class="absolute top-6 left-6">
                            <span class="px-4 py-2 {{ $destination->status ? 'bg-green-500/90' : 'bg-yellow-500/90' }} backdrop-blur-sm rounded-full text-white font-semibold">
                                {{ $destination->status_label }}
                            </span>
                        </div>

                        <!-- Season Badge -->
                        <div class="absolute top-6 right-6">
                            <span class="px-4 py-2 bg-white/20 backdrop-blur-sm rounded-full text-white font-semibold">
                                {{ $destination->season_label }}
                            </span>
                        </div>
                    </div>
                </div>

                <!-- Details Section -->
                <div class="glass rounded-2xl p-8" data-aos="fade-up" data-aos-delay="100">
                    <div class="mb-6">
                        <h1 class="text-4xl font-bold text-white mb-2">{{ $destination->nama_tempat }}</h1>
                        <p class="text-xl text-gray-300 mb-4">{{ $destination->negara }}</p>
                        
                        <!-- Mood Badge -->
                        <div class="inline-block">
                            <span class="px-4 py-2 bg-gradient-to-r from-cyan-500/20 to-purple-500/20 rounded-full text-cyan-300 font-semibold">
                                {{ $destination->mood_label }}
                            </span>
                        </div>
                    </div>

                    <!-- Description -->
                    <div class="mb-8">
                        <h3 class="text-lg font-semibold text-white mb-3">Deskripsi</h3>
                        <p class="text-gray-300 leading-relaxed">{{ $destination->deskripsi }}</p>
                    </div>

                    <!-- Meta Information -->
                    <div class="grid grid-cols-2 gap-4 mb-8">
                        <div class="bg-white/5 rounded-lg p-4">
                            <h4 class="text-sm font-medium text-gray-400 mb-1">Musim Terbaik</h4>
                            <p class="text-white font-semibold">{{ $destination->season_label }}</p>
                        </div>
                        <div class="bg-white/5 rounded-lg p-4">
                            <h4 class="text-sm font-medium text-gray-400 mb-1">Mood Perjalanan</h4>
                            <p class="text-white font-semibold">{{ $destination->mood_label }}</p>
                        </div>
                        <div class="bg-white/5 rounded-lg p-4">
                            <h4 class="text-sm font-medium text-gray-400 mb-1">Status</h4>
                            <p class="text-white font-semibold">{{ $destination->status_label }}</p>
                        </div>
                        <div class="bg-white/5 rounded-lg p-4">
                            <h4 class="text-sm font-medium text-gray-400 mb-1">Ditambahkan</h4>
                            <p class="text-white font-semibold">{{ $destination->created_at->format('d M Y') }}</p>
                        </div>
                    </div>

                    <!-- Action Buttons -->
                    <div class="flex space-x-4">
                        <a href="{{ route('destinations.edit', $destination) }}" 
                           class="flex-1 px-6 py-3 bg-gradient-to-r from-cyan-500 to-purple-500 rounded-xl text-white font-semibold text-center hover:from-cyan-600 hover:to-purple-600 transform hover:scale-105 transition-all duration-300 shadow-lg">
                            Edit Destinasi
                        </a>
                        <form action="{{ route('destinations.destroy', $destination) }}" 
                              method="POST" 
                              class="flex-1"
                              onsubmit="return confirm('Yakin ingin menghapus destinasi ini?')">
                            @csrf
                            @method('DELETE')
                            <button type="submit" 
                                    class="w-full px-6 py-3 bg-red-500 hover:bg-red-600 rounded-xl text-white font-semibold transition-colors">
                                Hapus Destinasi
                            </button>
                        </form>
                    </div>
                </div>
            </div>

            <!-- Additional Info -->
            <div class="mt-8 glass rounded-2xl p-8" data-aos="fade-up" data-aos-delay="200">
                <h3 class="text-2xl font-bold text-white mb-6">Informasi Tambahan</h3>
                
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <!-- Tips -->
                    <div class="bg-white/5 rounded-lg p-6">
                        <div class="w-12 h-12 bg-gradient-to-r from-blue-500 to-cyan-500 rounded-lg flex items-center justify-center mb-4">
                            <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                        </div>
                        <h4 class="text-lg font-semibold text-white mb-2">Tips Perjalanan</h4>
                        <p class="text-gray-300 text-sm">
                            @if($destination->musim == 'summer')
                                Bawa perlengkapan musim panas seperti sunscreen, topi, dan pakaian ringan.
                            @elseif($destination->musim == 'winter')
                                Siapkan pakaian hangat, jaket tebal, dan perlengkapan musim dingin.
                            @elseif($destination->musim == 'spring')
                                Musim semi adalah waktu yang sempurna dengan cuaca yang nyaman.
                            @else
                                Musim gugur menawarkan pemandangan yang indah dengan cuaca yang sejuk.
                            @endif
                        </p>
                    </div>

                    <!-- Budget -->
                    <div class="bg-white/5 rounded-lg p-6">
                        <div class="w-12 h-12 bg-gradient-to-r from-green-500 to-teal-500 rounded-lg flex items-center justify-center mb-4">
                            <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                            </svg>
                        </div>
                        <h4 class="text-lg font-semibold text-white mb-2">Estimasi Budget</h4>
                        <p class="text-gray-300 text-sm">
                            @if(str_contains(strtolower($destination->negara), 'indonesia'))
                                Budget domestik: Rp 2-10 juta tergantung durasi dan fasilitas.
                            @else
                                Budget internasional: Rp 15-50 juta tergantung negara dan durasi.
                            @endif
                        </p>
                    </div>

                    <!-- Duration -->
                    <div class="bg-white/5 rounded-lg p-6">
                        <div class="w-12 h-12 bg-gradient-to-r from-purple-500 to-pink-500 rounded-lg flex items-center justify-center mb-4">
                            <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                        </div>
                        <h4 class="text-lg font-semibold text-white mb-2">Durasi Ideal</h4>
                        <p class="text-gray-300 text-sm">
                            @if($destination->mood == 'healing')
                                5-7 hari untuk relaksasi dan penyembuhan yang optimal.
                            @elseif($destination->mood == 'adventure')
                                7-14 hari untuk eksplorasi dan petualangan yang menyeluruh.
                            @elseif($destination->mood == 'cultural')
                                4-7 hari untuk menjelajahi budaya dan sejarah.
                            @else
                                3-7 hari untuk menikmati pengalaman yang berkesan.
                            @endif
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</x-app-layout>
