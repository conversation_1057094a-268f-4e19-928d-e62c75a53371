<x-guest-layout>
    <div class="min-h-screen py-12">
        <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
            <!-- Header -->
            <div class="text-center mb-12" data-aos="fade-up">
                <h1 class="text-4xl font-bold gradient-text mb-4"><PERSON><PERSON><PERSON></h1>
                <p class="text-gray-300 text-lg">
                    Pelajari cara menggunakan Dream Destinations untuk mengelola destinasi impian <PERSON>a
                </p>
            </div>

            <!-- Guide Content -->
            <div class="space-y-8">
                <!-- Getting Started -->
                <div class="glass rounded-2xl p-8" data-aos="fade-up" data-aos-delay="100">
                    <h2 class="text-2xl font-bold text-white mb-6 flex items-center">
                        <svg class="w-8 h-8 text-cyan-400 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C20.832 18.477 19.246 18 17.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
                        </svg>
                        Memulai
                    </h2>
                    <div class="space-y-4 text-gray-300">
                        <div class="flex items-start space-x-4">
                            <span class="flex-shrink-0 w-8 h-8 bg-cyan-500 rounded-full flex items-center justify-center text-white font-bold">1</span>
                            <div>
                                <h3 class="font-semibold text-white">Daftar Akun</h3>
                                <p>Buat akun baru dengan mengklik tombol "Daftar" di halaman utama. Isi data diri Anda dengan lengkap.</p>
                            </div>
                        </div>
                        <div class="flex items-start space-x-4">
                            <span class="flex-shrink-0 w-8 h-8 bg-cyan-500 rounded-full flex items-center justify-center text-white font-bold">2</span>
                            <div>
                                <h3 class="font-semibold text-white">Login</h3>
                                <p>Masuk ke akun Anda menggunakan email dan password yang telah didaftarkan.</p>
                            </div>
                        </div>
                        <div class="flex items-start space-x-4">
                            <span class="flex-shrink-0 w-8 h-8 bg-cyan-500 rounded-full flex items-center justify-center text-white font-bold">3</span>
                            <div>
                                <h3 class="font-semibold text-white">Lengkapi Profil</h3>
                                <p>Upload foto profil dan lengkapi informasi pribadi Anda di halaman profil.</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Managing Destinations -->
                <div class="glass rounded-2xl p-8" data-aos="fade-up" data-aos-delay="200">
                    <h2 class="text-2xl font-bold text-white mb-6 flex items-center">
                        <svg class="w-8 h-8 text-purple-400 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                        </svg>
                        Mengelola Destinasi
                    </h2>
                    <div class="space-y-4 text-gray-300">
                        <div>
                            <h3 class="font-semibold text-white mb-2">Menambah Destinasi Baru</h3>
                            <p>Klik tombol "Tambah Destinasi" di dashboard atau halaman destinasi. Isi informasi lengkap seperti nama tempat, negara, musim terbaik, mood perjalanan, dan deskripsi.</p>
                        </div>
                        <div>
                            <h3 class="font-semibold text-white mb-2">Upload Gambar</h3>
                            <p>Anda dapat mengupload gambar destinasi sendiri atau sistem akan otomatis menggunakan gambar dari Unsplash.</p>
                        </div>
                        <div>
                            <h3 class="font-semibold text-white mb-2">Status Destinasi</h3>
                            <p>Tandai destinasi sebagai "Impian" atau "Sudah Dikunjungi" untuk melacak perjalanan Anda.</p>
                        </div>
                    </div>
                </div>

                <!-- Features -->
                <div class="glass rounded-2xl p-8" data-aos="fade-up" data-aos-delay="300">
                    <h2 class="text-2xl font-bold text-white mb-6 flex items-center">
                        <svg class="w-8 h-8 text-green-400 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                        Fitur Utama
                    </h2>
                    <div class="grid md:grid-cols-2 gap-6 text-gray-300">
                        <div>
                            <h3 class="font-semibold text-white mb-2">Dashboard</h3>
                            <p>Lihat statistik perjalanan Anda, destinasi terbaru, dan grafik mood perjalanan.</p>
                        </div>
                        <div>
                            <h3 class="font-semibold text-white mb-2">Galeri Global</h3>
                            <p>Jelajahi destinasi yang dibagikan oleh pengguna lain untuk inspirasi perjalanan.</p>
                        </div>
                        <div>
                            <h3 class="font-semibold text-white mb-2">Filter & Pencarian</h3>
                            <p>Cari destinasi berdasarkan nama, negara, musim, atau mood perjalanan.</p>
                        </div>
                        <div>
                            <h3 class="font-semibold text-white mb-2">Profil Personal</h3>
                            <p>Kelola informasi pribadi dan upload foto profil Anda.</p>
                        </div>
                    </div>
                </div>

                <!-- Tips -->
                <div class="glass rounded-2xl p-8" data-aos="fade-up" data-aos-delay="400">
                    <h2 class="text-2xl font-bold text-white mb-6 flex items-center">
                        <svg class="w-8 h-8 text-yellow-400 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path>
                        </svg>
                        Tips & Trik
                    </h2>
                    <div class="space-y-4 text-gray-300">
                        <div class="flex items-start space-x-3">
                            <span class="text-yellow-400">💡</span>
                            <p>Gunakan deskripsi yang detail untuk mengingat alasan mengapa Anda ingin mengunjungi tempat tersebut.</p>
                        </div>
                        <div class="flex items-start space-x-3">
                            <span class="text-yellow-400">💡</span>
                            <p>Pilih musim yang tepat untuk setiap destinasi agar perjalanan Anda lebih optimal.</p>
                        </div>
                        <div class="flex items-start space-x-3">
                            <span class="text-yellow-400">💡</span>
                            <p>Gunakan mood perjalanan untuk mengelompokkan destinasi berdasarkan jenis pengalaman yang diinginkan.</p>
                        </div>
                        <div class="flex items-start space-x-3">
                            <span class="text-yellow-400">💡</span>
                            <p>Jelajahi galeri global untuk mendapatkan inspirasi destinasi baru dari pengguna lain.</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- CTA -->
            <div class="text-center mt-12" data-aos="fade-up" data-aos-delay="500">
                @guest
                    <a href="{{ route('register') }}" class="inline-flex items-center px-8 py-4 bg-gradient-to-r from-cyan-500 to-purple-500 rounded-xl text-white font-semibold hover:from-cyan-600 hover:to-purple-600 transform hover:scale-105 transition-all duration-300 shadow-lg">
                        Mulai Sekarang
                        <svg class="w-5 h-5 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7l5 5m0 0l-5 5m5-5H6"></path>
                        </svg>
                    </a>
                @else
                    <a href="{{ route('dashboard') }}" class="inline-flex items-center px-8 py-4 bg-gradient-to-r from-cyan-500 to-purple-500 rounded-xl text-white font-semibold hover:from-cyan-600 hover:to-purple-600 transform hover:scale-105 transition-all duration-300 shadow-lg">
                        Ke Dashboard
                        <svg class="w-5 h-5 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7l5 5m0 0l-5 5m5-5H6"></path>
                        </svg>
                    </a>
                @endguest
            </div>
        </div>
    </div>
</x-guest-layout>
