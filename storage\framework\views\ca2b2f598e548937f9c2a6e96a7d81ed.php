<?php if (isset($component)) { $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54 = $attributes; } ?>
<?php $component = App\View\Components\AppLayout::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('app-layout'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\App\View\Components\AppLayout::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
    <div class="py-8">
        <div class="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
            <!-- Header -->
            <div class="glass rounded-2xl p-8 mb-8" data-aos="fade-up">
                <div class="flex items-center space-x-6">
                    <div class="w-20 h-20 rounded-2xl overflow-hidden border-4 border-cyan-500 profile-photo">
                        <img src="<?php echo e(auth()->user()->profile_photo_url); ?>"
                             alt="Profile Photo"
                             class="w-full h-full object-cover">
                    </div>
                    <div>
                        <h1 class="text-3xl font-bold text-white mb-2"><?php echo e(auth()->user()->name); ?></h1>
                        <p class="text-gray-300"><?php echo e(auth()->user()->email); ?></p>
                        <p class="text-sm text-gray-400">Bergabung <?php echo e(auth()->user()->created_at->format('d M Y')); ?></p>
                    </div>
                </div>
            </div>

            <!-- Statistics -->
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
                <div class="glass rounded-2xl p-6" data-aos="fade-up" data-aos-delay="100">
                    <div class="flex items-center">
                        <div class="w-12 h-12 bg-gradient-to-r from-blue-500 to-cyan-500 rounded-xl flex items-center justify-center mr-4">
                            <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                            </svg>
                        </div>
                        <div>
                            <p class="text-gray-300 text-sm">Total Destinasi</p>
                            <p class="text-2xl font-bold text-white"><?php echo e(auth()->user()->destinations()->count()); ?></p>
                        </div>
                    </div>
                </div>

                <div class="glass rounded-2xl p-6" data-aos="fade-up" data-aos-delay="200">
                    <div class="flex items-center">
                        <div class="w-12 h-12 bg-gradient-to-r from-purple-500 to-pink-500 rounded-xl flex items-center justify-center mr-4">
                            <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 3v4M3 5h4M6 17v4m-2-2h4m5-16l2.286 6.857L21 12l-5.714 2.143L13 21l-2.286-6.857L5 12l5.714-2.143L13 3z"></path>
                            </svg>
                        </div>
                        <div>
                            <p class="text-gray-300 text-sm">Destinasi Impian</p>
                            <p class="text-2xl font-bold text-white"><?php echo e(auth()->user()->dreamDestinations()->count()); ?></p>
                        </div>
                    </div>
                </div>

                <div class="glass rounded-2xl p-6" data-aos="fade-up" data-aos-delay="300">
                    <div class="flex items-center">
                        <div class="w-12 h-12 bg-gradient-to-r from-green-500 to-teal-500 rounded-xl flex items-center justify-center mr-4">
                            <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                            </svg>
                        </div>
                        <div>
                            <p class="text-gray-300 text-sm">Sudah Dikunjungi</p>
                            <p class="text-2xl font-bold text-white"><?php echo e(auth()->user()->visitedDestinations()->count()); ?></p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Profile Settings -->
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                <!-- Update Profile Information -->
                <div class="glass rounded-2xl p-8" data-aos="fade-up" data-aos-delay="400">
                    <h3 class="text-xl font-bold text-white mb-6">Informasi Profil</h3>
                    <?php echo $__env->make('profile.partials.update-profile-information-form', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
                </div>

                <!-- Update Password -->
                <div class="glass rounded-2xl p-8" data-aos="fade-up" data-aos-delay="500">
                    <h3 class="text-xl font-bold text-white mb-6">Ubah Password</h3>
                    <?php echo $__env->make('profile.partials.update-password-form', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
                </div>
            </div>

            <!-- Delete Account -->
            <div class="glass rounded-2xl p-8 mt-8" data-aos="fade-up" data-aos-delay="600">
                <h3 class="text-xl font-bold text-white mb-6">Hapus Akun</h3>
                <?php echo $__env->make('profile.partials.delete-user-form', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
            </div>
        </div>
    </div>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $attributes = $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $component = $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php /**PATH C:\Users\<USER>\Herd\abdulsomadm\resources\views/profile/edit.blade.php ENDPATH**/ ?>